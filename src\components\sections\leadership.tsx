"use client"

import { motion } from "framer-motion"
import { Users, Trophy, Calendar, Target, Award, Zap } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "../ui/card"

const leadershipRoles = [
  {
    title: "Tech Coordinator",
    organization: "Disha Committee",
    duration: "2023 - Present",
    description: "Leading a team of 70+ volunteers in organizing college-wide technical events and initiatives.",
    achievements: [
      "Managed 70+ volunteers across multiple technical domains",
      "Organized Manthan Fest - college's flagship technical festival",
      "Coordinated 15+ technical workshops and seminars",
      "Increased student participation by 200% in tech events",
      "Established partnerships with 10+ tech companies"
    ],
    icon: Users,
    color: "from-blue-500 to-purple-600"
  }
]

const achievements = [
  {
    title: "National Hackathon - 3rd Rank",
    description: "Secured 3rd position in a prestigious national-level hackathon competing against 500+ teams",
    icon: Trophy,
    color: "from-yellow-500 to-orange-600",
    year: "2024"
  },
  {
    title: "Hackathon Organizer",
    description: "Successfully organized and managed 4 national-level hackathons with 1000+ participants",
    icon: Target,
    color: "from-green-500 to-teal-600",
    year: "2023-2024"
  },
  {
    title: "Technical Event Management",
    description: "Led the organization of Manthan Fest, attracting 2000+ students from across the region",
    icon: Calendar,
    color: "from-purple-500 to-pink-600",
    year: "2023"
  }
]

export function Leadership() {
  return (
    <section id="leadership" className="py-20 relative">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Leadership & Achievements
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Driving innovation and fostering community growth through leadership
          </p>
        </motion.div>

        {/* Leadership Roles */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Leadership Experience
          </h3>
          
          {leadershipRoles.map((role, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="glass dark:glass-dark border-0 hover:scale-[1.02] transition-all duration-300">
                <CardHeader>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${role.color}`}>
                        <role.icon className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                          {role.title}
                        </CardTitle>
                        <p className="text-lg text-gray-600 dark:text-gray-400">
                          {role.organization}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                      <Calendar className="h-4 w-4" />
                      <span>{role.duration}</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {role.description}
                  </p>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                      Key Achievements:
                    </h4>
                    <ul className="space-y-3">
                      {role.achievements.map((achievement, i) => (
                        <motion.li
                          key={i}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: i * 0.1 }}
                          viewport={{ once: true }}
                          className="flex items-start space-x-3"
                        >
                          <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
                            {achievement}
                          </span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Achievements Grid */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">
            Notable Achievements
          </h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="glass dark:glass-dark border-0 h-full hover:scale-105 transition-all duration-300 group">
                  <CardContent className="p-6 text-center">
                    <div className={`inline-flex p-4 rounded-full bg-gradient-to-r ${achievement.color} mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <achievement.icon className="h-8 w-8 text-white" />
                    </div>
                    
                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      {achievement.title}
                    </h4>
                    
                    <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                      {achievement.description}
                    </p>
                    
                    <span className="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">
                      {achievement.year}
                    </span>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Impact Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6"
        >
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">70+</div>
            <div className="text-gray-600 dark:text-gray-400 text-sm">Team Members Led</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">4</div>
            <div className="text-gray-600 dark:text-gray-400 text-sm">Hackathons Organized</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">2000+</div>
            <div className="text-gray-600 dark:text-gray-400 text-sm">Students Impacted</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">15+</div>
            <div className="text-gray-600 dark:text-gray-400 text-sm">Events Organized</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}