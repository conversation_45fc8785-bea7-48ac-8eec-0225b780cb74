"use client"

import { motion } from "framer-motion"
import { GraduationCap, MapPin, Calendar } from "lucide-react"
import { Card, CardContent } from "../ui/card"

export function About() {
  return (
    <section id="about" className="py-20 relative">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            About Me
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Passionate about crafting digital experiences that make a difference
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Image and quick info */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="relative">
              <div className="w-80 h-80 mx-auto rounded-2xl overflow-hidden neo-brutal bg-gradient-to-br from-blue-400 to-purple-600 p-1">
                <img
                  src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400"
                  alt="Abhishek Kumar Singh"
                  className="w-full h-full object-cover rounded-xl"
                />
              </div>
            </div>
            
            <Card className="glass dark:glass-dark border-0">
              <CardContent className="p-6 space-y-4">
                <div className="flex items-center space-x-3">
                  <GraduationCap className="h-5 w-5 text-blue-600" />
                  <span className="text-gray-700 dark:text-gray-300">
                    B.Tech Computer Science, College of Engineering Roorkee
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-green-600" />
                  <span className="text-gray-700 dark:text-gray-300">
                    Graduating 2025
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-red-600" />
                  <span className="text-gray-700 dark:text-gray-300">
                    Roorkee, India
                  </span>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Right side - Description */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="prose prose-lg dark:prose-invert max-w-none">
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                I'm a passionate final-year Computer Science student with a strong focus on 
                <span className="font-semibold text-blue-600 dark:text-blue-400"> frontend development</span> and 
                full-stack capabilities. My journey in tech has been driven by curiosity and a desire to create 
                meaningful digital experiences.
              </p>
              
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                During my internship at <span className="font-semibold text-purple-600 dark:text-purple-400">IIT Roorkee</span>, 
                I had the opportunity to work on high-impact projects, revamping government portals and integrating 
                complex systems. This experience taught me the importance of scalable, maintainable code and 
                user-centered design.
              </p>
              
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                I'm particularly passionate about <span className="font-semibold text-green-600 dark:text-green-400">React.js</span>, 
                <span className="font-semibold text-blue-600 dark:text-blue-400"> Next.js</span>, and modern web technologies. 
                I enjoy solving complex problems, learning new technologies, and contributing to projects that make a 
                positive impact on users' lives.
              </p>
              
              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                When I'm not coding, you'll find me organizing tech events, mentoring fellow students, or exploring 
                the latest trends in web development and AI. I believe in continuous learning and sharing knowledge 
                with the community.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 mt-8">
              <motion.div
                className="text-center p-4 glass dark:glass-dark rounded-xl"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">30+</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">APIs Integrated</div>
              </motion.div>
              <motion.div
                className="text-center p-4 glass dark:glass-dark rounded-xl"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">45%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Bug Reduction</div>
              </motion.div>
              <motion.div
                className="text-center p-4 glass dark:glass-dark rounded-xl"
                whileHover={{ scale: 1.05 }}
              >
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">70+</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Team Members Led</div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}