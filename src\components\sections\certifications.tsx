"use client"

import { motion } from "framer-motion"
import { Award, ExternalLink, Calendar, CheckCircle } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "../ui/card"
import { But<PERSON> } from "../ui/button"

const certifications = [
  {
    title: "React Development",
    issuer: "Board Infinity",
    date: "2024",
    description: "Comprehensive certification covering React fundamentals, hooks, state management, and modern React patterns.",
    skills: ["React.js", "JSX", "Hooks", "State Management", "Component Architecture"],
    credentialUrl: "#",
    color: "from-blue-500 to-cyan-500",
    logo: "⚛️"
  },
  {
    title: "HTML, CSS & JavaScript",
    issuer: "Coursera",
    date: "2023",
    description: "Complete web development fundamentals covering HTML5, CSS3, and modern JavaScript ES6+ features.",
    skills: ["HTML5", "CSS3", "JavaScript", "DOM Manipulation", "Responsive Design"],
    credentialUrl: "#",
    color: "from-orange-500 to-red-500",
    logo: "🌐"
  },
  {
    title: "Cybersecurity Fundamentals",
    issuer: "IBM",
    date: "2023",
    description: "Comprehensive cybersecurity training covering threat analysis, security protocols, and best practices.",
    skills: ["Security Protocols", "Threat Analysis", "Risk Assessment", "Network Security"],
    credentialUrl: "#",
    color: "from-red-500 to-pink-500",
    logo: "🔒"
  },
  {
    title: "Java Programming",
    issuer: "GeeksforGeeks",
    date: "2023",
    description: "Advanced Java programming certification covering OOP concepts, data structures, and algorithms.",
    skills: ["Java", "OOP", "Data Structures", "Algorithms", "Problem Solving"],
    credentialUrl: "#",
    color: "from-green-500 to-teal-500",
    logo: "☕"
  },
  {
    title: "Full Stack Development Bootcamp",
    issuer: "GeeksforGeeks",
    date: "2024",
    description: "Intensive full-stack development program covering both frontend and backend technologies.",
    skills: ["Full Stack", "MERN Stack", "Database Design", "API Development", "Deployment"],
    credentialUrl: "#",
    color: "from-purple-500 to-indigo-500",
    logo: "🚀"
  }
]

export function Certifications() {
  return (
    <section id="certifications" className="py-20 relative">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Certifications
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Continuous learning and professional development achievements
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {certifications.map((cert, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${cert.color} text-2xl`}>
                        {cert.logo}
                      </div>
                      <div>
                        <CardTitle className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                          {cert.title}
                        </CardTitle>
                        <p className="text-gray-600 dark:text-gray-400 font-medium">
                          {cert.issuer}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                      <Calendar className="h-4 w-4" />
                      <span className="text-sm">{cert.date}</span>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {cert.description}
                  </p>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                      <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                      Skills Covered:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {cert.skills.map((skill, i) => (
                        <motion.span
                          key={i}
                          initial={{ opacity: 0, scale: 0.8 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3, delay: i * 0.05 }}
                          viewport={{ once: true }}
                          className="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200"
                        >
                          {skill}
                        </motion.span>
                      ))}
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full group-hover:scale-105 transition-transform duration-200"
                      onClick={() => window.open(cert.credentialUrl, '_blank')}
                    >
                      <Award className="mr-2 h-4 w-4" />
                      View Credential
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Certification Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <Award className="h-8 w-8 text-yellow-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">5+</div>
            <div className="text-gray-600 dark:text-gray-400">Professional Certifications</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">100%</div>
            <div className="text-gray-600 dark:text-gray-400">Completion Rate</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <Calendar className="h-8 w-8 text-blue-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">2024</div>
            <div className="text-gray-600 dark:text-gray-400">Latest Certification</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}