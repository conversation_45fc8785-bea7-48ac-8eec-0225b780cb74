import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { join } from 'path'

export async function GET(request: NextRequest) {
  try {
    // Get the resume file path
    const resumePath = join(process.cwd(), 'public', 'resume.pdf')
    
    // Read the file
    const fileBuffer = await readFile(resumePath)
    
    // Log download for analytics (optional)
    console.log(`Resume downloaded at ${new Date().toISOString()}`)
    
    // Return the file with proper headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'attachment; filename="<PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>_<PERSON>_Resume.pdf"',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    })
  } catch (error) {
    console.error('Error serving resume:', error)
    return NextResponse.json(
      { error: 'Resume not found' },
      { status: 404 }
    )
  }
}
