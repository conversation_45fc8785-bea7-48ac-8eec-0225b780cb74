"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/floating-shapes.tsx":
/*!********************************************!*\
  !*** ./src/components/floating-shapes.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingShapes: () => (/* binding */ FloatingShapes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ FloatingShapes auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FloatingShapes() {\n    _s();\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingShapes.useEffect\": ()=>{\n            const generateParticles = {\n                \"FloatingShapes.useEffect.generateParticles\": ()=>{\n                    const newParticles = [];\n                    const colors = [\n                        'from-blue-400 to-purple-500',\n                        'from-pink-400 to-red-500',\n                        'from-green-400 to-blue-500',\n                        'from-yellow-400 to-orange-500',\n                        'from-purple-400 to-pink-500',\n                        'from-cyan-400 to-blue-500',\n                        'from-indigo-400 to-purple-500',\n                        'from-emerald-400 to-teal-500'\n                    ];\n                    const shapes = [\n                        'circle',\n                        'triangle',\n                        'square',\n                        'diamond',\n                        'hexagon'\n                    ];\n                    for(let i = 0; i < 12; i++){\n                        newParticles.push({\n                            id: i,\n                            x: Math.random() * 100,\n                            y: Math.random() * 100,\n                            size: Math.random() * 60 + 20,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            duration: Math.random() * 20 + 15,\n                            delay: Math.random() * 5,\n                            shape: shapes[Math.floor(Math.random() * shapes.length)]\n                        });\n                    }\n                    setParticles(newParticles);\n                }\n            }[\"FloatingShapes.useEffect.generateParticles\"];\n            generateParticles();\n        }\n    }[\"FloatingShapes.useEffect\"], []);\n    const getShapeClipPath = (shape)=>{\n        switch(shape){\n            case 'triangle':\n                return 'polygon(50% 0%, 0% 100%, 100% 100%)';\n            case 'square':\n                return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';\n            case 'diamond':\n                return 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)';\n            case 'hexagon':\n                return 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)';\n            default:\n                return 'none';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 overflow-hidden pointer-events-none z-0\",\n        children: [\n            particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"absolute bg-gradient-to-r \".concat(particle.color, \" opacity-15 blur-[0.5px]\"),\n                    style: {\n                        left: \"\".concat(particle.x, \"%\"),\n                        top: \"\".concat(particle.y, \"%\"),\n                        width: \"\".concat(particle.size, \"px\"),\n                        height: \"\".concat(particle.size, \"px\"),\n                        clipPath: getShapeClipPath(particle.shape),\n                        borderRadius: particle.shape === 'circle' ? '50%' : '8px'\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -40,\n                            20,\n                            -30,\n                            0\n                        ],\n                        x: [\n                            0,\n                            30,\n                            -20,\n                            25,\n                            0\n                        ],\n                        rotate: [\n                            0,\n                            180,\n                            270,\n                            360\n                        ],\n                        scale: [\n                            1,\n                            1.2,\n                            0.8,\n                            1.1,\n                            1\n                        ],\n                        opacity: [\n                            0.15,\n                            0.25,\n                            0.1,\n                            0.2,\n                            0.15\n                        ]\n                    },\n                    transition: {\n                        duration: particle.duration,\n                        repeat: Infinity,\n                        ease: \"easeInOut\",\n                        delay: particle.delay\n                    }\n                }, particle.id, false, {\n                    fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/4 left-1/4 w-32 h-32 holographic rounded-full opacity-20 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    x: [\n                        0,\n                        40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-3/4 right-1/4 w-24 h-24 holographic rounded-full opacity-25 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 2\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/2 right-1/3 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 morphing\",\n                animate: {\n                    y: [\n                        0,\n                        -25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        25,\n                        0\n                    ],\n                    rotate: [\n                        0,\n                        90,\n                        180,\n                        270,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 30,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/3 right-1/5 w-16 h-16 glass-ultra rounded-lg opacity-30\",\n                animate: {\n                    y: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        0\n                    ],\n                    rotateY: [\n                        0,\n                        180,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute bottom-1/3 left-1/5 w-12 h-12 glass-ultra rounded-full opacity-25\",\n                animate: {\n                    y: [\n                        0,\n                        25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.4,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 22,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 3\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingShapes, \"n2oV9J0JxRF0n1eg4nXLNJcP/RY=\");\n_c = FloatingShapes;\nvar _c;\n$RefreshReg$(_c, \"FloatingShapes\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/floating-shapes.tsx\n"));

/***/ })

});