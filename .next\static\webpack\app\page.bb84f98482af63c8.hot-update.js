"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/floating-shapes.tsx":
/*!********************************************!*\
  !*** ./src/components/floating-shapes.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FloatingShapes: () => (/* binding */ FloatingShapes)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _no_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./no-ssr */ \"(app-pages-browser)/./src/components/no-ssr.tsx\");\n/* __next_internal_client_entry_do_not_use__ FloatingShapes auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction FloatingShapesContent() {\n    _s();\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingShapesContent.useEffect\": ()=>{\n            const generateParticles = {\n                \"FloatingShapesContent.useEffect.generateParticles\": ()=>{\n                    const newParticles = [];\n                    const colors = [\n                        'from-blue-400 to-purple-500',\n                        'from-pink-400 to-red-500',\n                        'from-green-400 to-blue-500',\n                        'from-yellow-400 to-orange-500',\n                        'from-purple-400 to-pink-500',\n                        'from-cyan-400 to-blue-500',\n                        'from-indigo-400 to-purple-500',\n                        'from-emerald-400 to-teal-500'\n                    ];\n                    const shapes = [\n                        'circle',\n                        'triangle',\n                        'square',\n                        'diamond',\n                        'hexagon'\n                    ];\n                    for(let i = 0; i < 12; i++){\n                        newParticles.push({\n                            id: i,\n                            x: Math.random() * 100,\n                            y: Math.random() * 100,\n                            size: Math.random() * 60 + 20,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            duration: Math.random() * 20 + 15,\n                            delay: Math.random() * 5,\n                            shape: shapes[Math.floor(Math.random() * shapes.length)]\n                        });\n                    }\n                    setParticles(newParticles);\n                }\n            }[\"FloatingShapesContent.useEffect.generateParticles\"];\n            generateParticles();\n        }\n    }[\"FloatingShapesContent.useEffect\"], []);\n    const getShapeClipPath = (shape)=>{\n        switch(shape){\n            case 'triangle':\n                return 'polygon(50% 0%, 0% 100%, 100% 100%)';\n            case 'square':\n                return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';\n            case 'diamond':\n                return 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)';\n            case 'hexagon':\n                return 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)';\n            default:\n                return 'none';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 overflow-hidden pointer-events-none z-0\",\n        children: [\n            particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"absolute bg-gradient-to-r \".concat(particle.color, \" opacity-15 blur-[0.5px]\"),\n                    style: {\n                        left: \"\".concat(particle.x, \"%\"),\n                        top: \"\".concat(particle.y, \"%\"),\n                        width: \"\".concat(particle.size, \"px\"),\n                        height: \"\".concat(particle.size, \"px\"),\n                        clipPath: getShapeClipPath(particle.shape),\n                        borderRadius: particle.shape === 'circle' ? '50%' : '8px'\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -40,\n                            20,\n                            -30,\n                            0\n                        ],\n                        x: [\n                            0,\n                            30,\n                            -20,\n                            25,\n                            0\n                        ],\n                        rotate: [\n                            0,\n                            180,\n                            270,\n                            360\n                        ],\n                        scale: [\n                            1,\n                            1.2,\n                            0.8,\n                            1.1,\n                            1\n                        ],\n                        opacity: [\n                            0.15,\n                            0.25,\n                            0.1,\n                            0.2,\n                            0.15\n                        ]\n                    },\n                    transition: {\n                        duration: particle.duration,\n                        repeat: Infinity,\n                        ease: \"easeInOut\",\n                        delay: particle.delay\n                    }\n                }, particle.id, false, {\n                    fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute top-1/4 left-1/4 w-32 h-32 holographic rounded-full opacity-20 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    x: [\n                        0,\n                        40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute top-3/4 right-1/4 w-24 h-24 holographic rounded-full opacity-25 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 2\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute top-1/2 right-1/3 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 morphing\",\n                animate: {\n                    y: [\n                        0,\n                        -25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        25,\n                        0\n                    ],\n                    rotate: [\n                        0,\n                        90,\n                        180,\n                        270,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 30,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute top-1/3 right-1/5 w-16 h-16 glass-ultra rounded-lg opacity-30\",\n                animate: {\n                    y: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        0\n                    ],\n                    rotateY: [\n                        0,\n                        180,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute bottom-1/3 left-1/5 w-12 h-12 glass-ultra rounded-full opacity-25\",\n                animate: {\n                    y: [\n                        0,\n                        25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.4,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 22,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 3\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingShapesContent, \"n2oV9J0JxRF0n1eg4nXLNJcP/RY=\");\n_c = FloatingShapesContent;\nfunction FloatingShapes() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_no_ssr__WEBPACK_IMPORTED_MODULE_2__.NoSSR, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 overflow-hidden pointer-events-none z-0\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n            lineNumber: 183,\n            columnNumber: 22\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FloatingShapesContent, {}, void 0, false, {\n            fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_c1 = FloatingShapes;\nvar _c, _c1;\n$RefreshReg$(_c, \"FloatingShapesContent\");\n$RefreshReg$(_c1, \"FloatingShapes\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/floating-shapes.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/no-ssr.tsx":
/*!***********************************!*\
  !*** ./src/components/no-ssr.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoSSR: () => (/* binding */ NoSSR)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ NoSSR auto */ \nvar _s = $RefreshSig$();\n\nfunction NoSSR(param) {\n    let { children, fallback = null } = param;\n    _s();\n    const [hasMounted, setHasMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoSSR.useEffect\": ()=>{\n            setHasMounted(true);\n        }\n    }[\"NoSSR.useEffect\"], []);\n    if (!hasMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: fallback\n        }, void 0, false);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(NoSSR, \"aiSd/DQPOnbbLLZZL0Xv/KtPBDg=\");\n_c = NoSSR;\nvar _c;\n$RefreshReg$(_c, \"NoSSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL25vLXNzci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBRTJDO0FBT3BDLFNBQVNFLE1BQU0sS0FBeUM7UUFBekMsRUFBRUMsUUFBUSxFQUFFQyxXQUFXLElBQUksRUFBYyxHQUF6Qzs7SUFDcEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdMLCtDQUFRQSxDQUFDO0lBRTdDRCxnREFBU0E7MkJBQUM7WUFDUk0sY0FBYztRQUNoQjswQkFBRyxFQUFFO0lBRUwsSUFBSSxDQUFDRCxZQUFZO1FBQ2YscUJBQU87c0JBQUdEOztJQUNaO0lBRUEscUJBQU87a0JBQUdEOztBQUNaO0dBWmdCRDtLQUFBQSIsInNvdXJjZXMiOlsiRDpcXFBvcnRmb2xpb18xXFxzcmNcXGNvbXBvbmVudHNcXG5vLXNzci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgTm9TU1JQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbiAgZmFsbGJhY2s/OiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIE5vU1NSKHsgY2hpbGRyZW4sIGZhbGxiYWNrID0gbnVsbCB9OiBOb1NTUlByb3BzKSB7XG4gIGNvbnN0IFtoYXNNb3VudGVkLCBzZXRIYXNNb3VudGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0SGFzTW91bnRlZCh0cnVlKVxuICB9LCBbXSlcblxuICBpZiAoIWhhc01vdW50ZWQpIHtcbiAgICByZXR1cm4gPD57ZmFsbGJhY2t9PC8+XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJOb1NTUiIsImNoaWxkcmVuIiwiZmFsbGJhY2siLCJoYXNNb3VudGVkIiwic2V0SGFzTW91bnRlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/no-ssr.tsx\n"));

/***/ })

});