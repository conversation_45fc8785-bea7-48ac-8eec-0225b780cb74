"use client"

import { motion } from "framer-motion"
import { ExternalLink, Github, Calendar, Users, TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "../ui/card"
import { But<PERSON> } from "../ui/button"

const projects = [
  {
    title: "Threat Zone Prediction System",
    description: "A comprehensive real-time disaster alert platform that leverages machine learning models to predict and monitor potential threat zones with 88% accuracy.",
    longDescription: "Built a sophisticated disaster management system using React for the frontend, Node.js for backend services, and MongoDB for data storage. Integrated multiple ML models for threat prediction and real-time monitoring capabilities.",
    technologies: ["React", "Node.js", "MongoDB", "Machine Learning", "Python", "Express.js"],
    features: [
      "Real-time threat monitoring and alerts",
      "88% accuracy in disaster prediction",
      "Interactive dashboard with data visualization",
      "Multi-source data integration",
      "Mobile-responsive design"
    ],
    liveUrl: "https://threat-sigma.vercel.app",
    githubUrl: "https://github.com/Abhishek-kumarsingh/Threat-Zone-Prediction",
    status: "Completed",
    color: "from-red-500 to-orange-600",
    image: "https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "AI Interview Platform",
    description: "An intelligent interview platform powered by AI that provides real-time feedback, code evaluation, and comprehensive analytics for both candidates and recruiters.",
    longDescription: "Developed using Next.js with Gemini API integration for AI-powered interviews. Features Monaco Editor for code challenges, JWT OAuth for secure authentication, and detailed analytics dashboards.",
    technologies: ["Next.js", "Gemini API", "Monaco Editor", "JWT", "OAuth", "TypeScript"],
    features: [
      "AI-powered interview questions",
      "Real-time code evaluation",
      "Comprehensive analytics dashboard",
      "Secure authentication system",
      "Multi-language code support"
    ],
    liveUrl: "https://aithors.vercel.app",
    githubUrl: "https://github.com/Abhishek-kumarsingh/Aithors",
    status: "Completed",
    color: "from-blue-500 to-purple-600",
    image: "https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "Learning Management System",
    description: "A comprehensive LMS platform with payment integration, real-time communication, and advanced course management features for educators and students.",
    longDescription: "Currently developing a full-featured LMS using Next.js for the frontend, Spring Boot for backend services, Stripe for payment processing, and RabbitMQ for real-time messaging and notifications.",
    technologies: ["Next.js", "Spring Boot", "Stripe", "RabbitMQ", "PostgreSQL", "Redis"],
    features: [
      "Course creation and management",
      "Integrated payment system",
      "Real-time messaging and notifications",
      "Progress tracking and analytics",
      "Multi-role user management"
    ],
    liveUrl: "https://lms-neon-tau.vercel.app",
    githubUrl: "https://github.com/Abhishek-kumarsingh/LMS-Platform",
    status: "In Progress",
    color: "from-green-500 to-teal-600",
    image: "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=600"
  },
  {
    title: "Threat Monitoring System",
    description: "Advanced threat monitoring system with real-time data processing, machine learning predictions, and comprehensive visualization dashboards.",
    longDescription: "Building a sophisticated monitoring system using FastAPI for high-performance backend, Spring Boot for microservices, Docker for containerization, and XGBoost for predictive analytics.",
    technologies: ["FastAPI", "Spring Boot", "Docker", "Recharts", "WebSockets", "XGBoost"],
    features: [
      "Real-time threat detection",
      "Machine learning predictions",
      "Interactive data visualization",
      "Microservices architecture",
      "Scalable containerized deployment"
    ],
    liveUrl: "https://threat-monitoring.vercel.app",
    githubUrl: "https://github.com/Abhishek-kumarsingh/Threat-Monitoring-System",
    status: "In Progress",
    color: "from-purple-500 to-pink-600",
    image: "https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=600"
  }
]

export function Projects() {
  return (
    <section id="projects" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold gradient-text mb-6">
            Featured Projects
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Showcasing innovative solutions and technical expertise
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="card-premium border-0 h-full group hover-lift-premium hover-tilt">
                <CardHeader className="pb-4">
                  <div className="relative overflow-hidden rounded-lg mb-4">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                    <div className="absolute top-4 right-4">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${project.color} text-white`}>
                        {project.status}
                      </span>
                    </div>
                  </div>
                  
                  <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    {project.title}
                  </CardTitle>
                  
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {project.description}
                  </p>
                </CardHeader>
                
                <CardContent className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4>
                    <ul className="space-y-2">
                      {project.features.map((feature, i) => (
                        <li key={i} className="flex items-start space-x-2">
                          <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {feature}
                          </span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, i) => (
                      <span
                        key={i}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                  
                  <div className="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => window.open(project.liveUrl, '_blank')}
                    >
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Live Demo
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => window.open(project.githubUrl, '_blank')}
                    >
                      <Github className="mr-2 h-4 w-4" />
                      Source Code
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Project Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <Calendar className="h-8 w-8 text-blue-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">4+</div>
            <div className="text-gray-600 dark:text-gray-400">Projects Completed</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">88%</div>
            <div className="text-gray-600 dark:text-gray-400">ML Model Accuracy</div>
          </div>
          
          <div className="text-center p-6 glass dark:glass-dark rounded-xl">
            <Users className="h-8 w-8 text-purple-600 mx-auto mb-4" />
            <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">1000+</div>
            <div className="text-gray-600 dark:text-gray-400">Users Impacted</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}