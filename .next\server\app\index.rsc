1:"$Sreact.fragment"
2:I[1483,["177","static/chunks/app/layout-f41564b1781e53a9.js"],"ThemeProvider"]
3:I[7555,[],""]
4:I[1295,[],""]
5:I[2836,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"FloatingShapes"]
6:I[1440,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Navigation"]
7:I[7711,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"<PERSON>"]
8:I[4334,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"About"]
9:I[9067,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Experience"]
a:I[8957,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Projects"]
b:I[8397,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Skills"]
c:I[9530,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Leadership"]
d:I[1634,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Certifications"]
e:I[433,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Contact"]
f:I[3430,["327","static/chunks/327-98c3c5aa4ea6ce55.js","974","static/chunks/app/page-fe2c1977feea57b0.js"],"Footer"]
10:I[9665,[],"OutletBoundary"]
12:I[4911,[],"AsyncMetadataOutlet"]
14:I[9665,[],"ViewportBoundary"]
16:I[9665,[],"MetadataBoundary"]
17:"$Sreact.suspense"
19:I[8393,[],""]
:HL["/_next/static/css/641cf9563d92ca3b.css","style"]
0:{"P":null,"b":"l4Q0NMTmhHtPBXa2ZyD85","p":"","c":["",""],"i":false,"f":[[["",{"children":["__PAGE__",{}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/641cf9563d92ca3b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L2",null,{"attribute":"class","defaultTheme":"system","enableSystem":true,"disableTransitionOnChange":true,"children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}]}]}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","main",null,{"className":"relative min-h-screen","children":[["$","$L5",null,{}],["$","$L6",null,{}],["$","$L7",null,{}],["$","$L8",null,{}],["$","$L9",null,{}],["$","$La",null,{}],["$","$Lb",null,{}],["$","$Lc",null,{}],["$","$Ld",null,{}],["$","$Le",null,{}],["$","$Lf",null,{}]]}],null,["$","$L10",null,{"children":["$L11",["$","$L12",null,{"promise":"$@13"}]]}]]}],{},null,false]},null,false],["$","$1","h",{"children":[null,[["$","$L14",null,{"children":"$L15"}],null],["$","$L16",null,{"children":["$","div",null,{"hidden":true,"children":["$","$17",null,{"fallback":null,"children":"$L18"}]}]}]]}],false]],"m":"$undefined","G":["$19",[]],"s":false,"S":true}
15:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
11:null
13:{"metadata":[["$","title","0",{"children":"Abhishek Kumar Singh – Fullstack Developer & Frontend Enthusiast"}],["$","meta","1",{"name":"description","content":"Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies."}],["$","meta","2",{"name":"author","content":"Abhishek Kumar Singh"}],["$","meta","3",{"name":"keywords","content":"Abhishek Kumar Singh,Frontend Developer,Full Stack Developer,React Developer,Next.js,IIT Roorkee,College of Engineering Roorkee,Computer Science,Web Developer,JavaScript,TypeScript,Tailwind CSS,Portfolio"}],["$","meta","4",{"name":"creator","content":"Abhishek Kumar Singh"}],["$","meta","5",{"name":"robots","content":"index, follow"}],["$","meta","6",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","7",{"name":"resume-url","content":"/resume.pdf"}],["$","meta","8",{"name":"resume-download","content":"/api/resume"}],["$","meta","9",{"name":"google-site-verification","content":"your-google-verification-code"}],["$","meta","10",{"property":"og:title","content":"Abhishek Kumar Singh – Fullstack Developer & Frontend Enthusiast"}],["$","meta","11",{"property":"og:description","content":"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies."}],["$","meta","12",{"property":"og:url","content":"https://abhishek-portfolio.vercel.app"}],["$","meta","13",{"property":"og:site_name","content":"Abhishek Kumar Singh Portfolio"}],["$","meta","14",{"property":"og:locale","content":"en_US"}],["$","meta","15",{"property":"og:image","content":"http://localhost:3000/og-image.jpg"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"Abhishek Kumar Singh - Portfolio"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:title","content":"Abhishek Kumar Singh – Fullstack Developer & Frontend Enthusiast"}],["$","meta","22",{"name":"twitter:description","content":"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences."}],["$","meta","23",{"name":"twitter:image","content":"http://localhost:3000/og-image.jpg"}]],"error":null,"digest":"$undefined"}
18:"$13:metadata"
