"use client"

import { motion } from "framer-motion"
import { useEffect, useState } from "react"

interface Particle {
  id: number
  x: number
  y: number
  size: number
  color: string
  duration: number
  delay: number
  shape: 'circle' | 'triangle' | 'square' | 'diamond' | 'hexagon'
}

export function FloatingShapes() {
  const [particles, setParticles] = useState<Particle[]>([])

  useEffect(() => {
    const generateParticles = () => {
      const newParticles: Particle[] = []
      const colors = [
        'from-blue-400 to-purple-500',
        'from-pink-400 to-red-500',
        'from-green-400 to-blue-500',
        'from-yellow-400 to-orange-500',
        'from-purple-400 to-pink-500',
        'from-cyan-400 to-blue-500',
        'from-indigo-400 to-purple-500',
        'from-emerald-400 to-teal-500'
      ]

      const shapes: Particle['shape'][] = ['circle', 'triangle', 'square', 'diamond', 'hexagon']

      for (let i = 0; i < 12; i++) {
        newParticles.push({
          id: i,
          x: Math.random() * 100,
          y: Math.random() * 100,
          size: Math.random() * 60 + 20,
          color: colors[Math.floor(Math.random() * colors.length)],
          duration: Math.random() * 20 + 15,
          delay: Math.random() * 5,
          shape: shapes[Math.floor(Math.random() * shapes.length)]
        })
      }

      setParticles(newParticles)
    }

    generateParticles()
  }, [])

  const getShapeClipPath = (shape: Particle['shape']) => {
    switch (shape) {
      case 'triangle':
        return 'polygon(50% 0%, 0% 100%, 100% 100%)'
      case 'square':
        return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)'
      case 'diamond':
        return 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)'
      case 'hexagon':
        return 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)'
      default:
        return 'none'
    }
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {/* Advanced Particle System */}
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className={`absolute bg-gradient-to-r ${particle.color} opacity-15 blur-[0.5px]`}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            clipPath: getShapeClipPath(particle.shape),
            borderRadius: particle.shape === 'circle' ? '50%' : '8px',
          }}
          animate={{
            y: [0, -40, 20, -30, 0],
            x: [0, 30, -20, 25, 0],
            rotate: [0, 180, 270, 360],
            scale: [1, 1.2, 0.8, 1.1, 1],
            opacity: [0.15, 0.25, 0.1, 0.2, 0.15],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            ease: "easeInOut",
            delay: particle.delay,
          }}
        />
      ))}

      {/* Holographic Orbs */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-32 h-32 holographic rounded-full opacity-20 blur-sm"
        animate={{
          y: [0, -50, 0],
          x: [0, 40, 0],
          scale: [1, 1.3, 1],
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      <motion.div
        className="absolute top-3/4 right-1/4 w-24 h-24 holographic rounded-full opacity-25 blur-sm"
        animate={{
          y: [0, 30, 0],
          x: [0, -30, 0],
          scale: [1, 0.8, 1],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />

      {/* Morphing Shapes */}
      <motion.div
        className="absolute top-1/2 right-1/3 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 morphing"
        animate={{
          y: [0, -25, 0],
          x: [0, 25, 0],
          rotate: [0, 90, 180, 270, 360],
        }}
        transition={{
          duration: 30,
          repeat: Infinity,
          ease: "linear",
        }}
      />

      {/* Floating Glass Elements */}
      <motion.div
        className="absolute top-1/3 right-1/5 w-16 h-16 glass-ultra rounded-lg opacity-30"
        animate={{
          y: [0, -20, 0],
          x: [0, 15, 0],
          rotateY: [0, 180, 360],
        }}
        transition={{
          duration: 18,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />

      <motion.div
        className="absolute bottom-1/3 left-1/5 w-12 h-12 glass-ultra rounded-full opacity-25"
        animate={{
          y: [0, 25, 0],
          x: [0, -20, 0],
          scale: [1, 1.4, 1],
        }}
        transition={{
          duration: 22,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 3,
        }}
      />
    </div>
  )
}