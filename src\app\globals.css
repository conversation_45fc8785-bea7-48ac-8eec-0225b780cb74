@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Enhanced Glassmorphism Effects */
  .glass {
    @apply bg-white/20 backdrop-blur-xl border border-white/30 shadow-lg;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .glass-dark {
    @apply bg-black/20 backdrop-blur-xl border border-white/20 shadow-xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .glass-card {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 shadow-2xl rounded-2xl;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .glass-card-dark {
    @apply bg-black/10 backdrop-blur-lg border border-white/10 shadow-2xl rounded-2xl;
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  /* Enhanced Neo-Brutalism Effects */
  .neo-brutal {
    @apply border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] bg-white;
    transform: translate(0, 0);
    transition: all 0.2s ease;
  }

  .neo-brutal-hover {
    @apply hover:shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-4px] hover:translate-y-[-4px];
  }

  .neo-brutal-dark {
    @apply border-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] bg-gray-900;
    transform: translate(0, 0);
    transition: all 0.2s ease;
  }

  .neo-brutal-dark-hover {
    @apply hover:shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] hover:translate-x-[-4px] hover:translate-y-[-4px];
  }

  /* Enhanced Gradient Effects */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  .gradient-text-alt {
    @apply bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 bg-clip-text text-transparent;
    background-size: 200% 200%;
    animation: gradient 4s ease infinite;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50;
  }

  .gradient-bg-dark {
    @apply bg-gradient-to-br from-blue-950/20 via-purple-950/20 to-pink-950/20;
  }

  .gradient-border {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
    padding: 2px;
    border-radius: 12px;
  }

  .gradient-border-content {
    @apply bg-white dark:bg-gray-900 rounded-[10px] w-full h-full;
  }
}

html {
  scroll-behavior: smooth;
}

.typing-cursor::after {
  content: '|';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Enhanced Animations and Effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
  }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Additional Utility Classes */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hover-lift-dark:hover {
  box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-dark {
  text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.1);
}

.glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.floating {
  animation: float 6s ease-in-out infinite;
}

/* Enhanced Button Styles */
.btn-neo {
  @apply neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold px-6 py-3 rounded-lg;
  transition: all 0.2s ease;
}

.btn-neo:active {
  transform: translate(4px, 4px);
  box-shadow: 4px 4px 0px 0px rgba(0, 0, 0, 1);
}

.btn-glass {
  @apply glass hover:glass-dark transition-all duration-300 px-6 py-3 rounded-lg font-medium;
}

/* Card Enhancements */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
}

/* Responsive Glassmorphism */
@media (max-width: 768px) {
  .glass, .glass-dark {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}