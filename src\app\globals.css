@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

@layer base {
  :root {
    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    /* Advanced Design System Variables */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    --glass-blur: 16px;

    --neo-shadow-primary: 8px 8px 0px rgba(0, 0, 0, 1);
    --neo-shadow-hover: 12px 12px 0px rgba(0, 0, 0, 1);
    --neo-shadow-active: 4px 4px 0px rgba(0, 0, 0, 1);
    --neo-border: 4px solid #000;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warm: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;

    /* Dark Mode Overrides */
    --glass-bg: rgba(0, 0, 0, 0.12);
    --glass-border: rgba(255, 255, 255, 0.12);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    --neo-shadow-primary: 8px 8px 0px rgba(255, 255, 255, 0.9);
    --neo-shadow-hover: 12px 12px 0px rgba(255, 255, 255, 0.9);
    --neo-shadow-active: 4px 4px 0px rgba(255, 255, 255, 0.9);
    --neo-border: 4px solid rgba(255, 255, 255, 0.9);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* ===== ADVANCED GLASSMORPHISM SYSTEM ===== */
  .glass-ultra {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    position: relative;
    overflow: hidden;
  }

  .glass-ultra::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  .glass-card-premium {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
  }

  .glass-card-premium::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    pointer-events: none;
  }

  .glass-morphism-advanced {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 100%);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow:
      0 8px 32px rgba(31, 38, 135, 0.37),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(16px) saturate(180%) contrast(120%);
    -webkit-backdrop-filter: blur(16px) saturate(180%) contrast(120%);
    border-radius: 12px;
  }

  /* ===== ADVANCED NEO-BRUTALISM SYSTEM ===== */
  .neo-brutal-premium {
    border: var(--neo-border);
    box-shadow: var(--neo-shadow-primary);
    background: #fff;
    transform: translate(0, 0);
    transition: all var(--transition-fast) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .neo-brutal-premium:hover {
    box-shadow: var(--neo-shadow-hover);
    transform: translate(-4px, -4px);
  }

  .neo-brutal-premium:active {
    box-shadow: var(--neo-shadow-active);
    transform: translate(4px, 4px);
  }

  .neo-brutal-gradient {
    border: 4px solid transparent;
    background: linear-gradient(#fff, #fff) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
    box-shadow: 8px 8px 0px #000;
    transition: all var(--transition-fast);
  }

  .neo-brutal-gradient:hover {
    box-shadow: 12px 12px 0px #000;
    transform: translate(-4px, -4px);
  }

  .neo-brutal-3d {
    border: 4px solid #000;
    background: linear-gradient(145deg, #ffffff, #e6e6e6);
    box-shadow:
      8px 8px 0px #000,
      inset 2px 2px 4px rgba(255, 255, 255, 0.8),
      inset -2px -2px 4px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-fast);
  }

  .neo-brutal-3d:hover {
    box-shadow:
      12px 12px 0px #000,
      inset 2px 2px 4px rgba(255, 255, 255, 0.8),
      inset -2px -2px 4px rgba(0, 0, 0, 0.1);
    transform: translate(-4px, -4px);
  }
}

html {
  scroll-behavior: smooth;
}

.typing-cursor::after {
  content: '|';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

  /* ===== ADVANCED GRADIENT SYSTEM ===== */
  .gradient-text-premium {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 4s ease infinite;
  }

  .gradient-text-rainbow {
    background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
    background-size: 400% 400%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: rainbow 3s linear infinite;
  }

  .gradient-border-animated {
    position: relative;
    background: #fff;
    border-radius: 12px;
    padding: 2px;
  }

  .gradient-border-animated::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    background-size: 300% 300%;
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    animation: gradient-rotate 3s linear infinite;
  }

  .holographic {
    background: linear-gradient(45deg,
      hsl(240, 100%, 50%) 0%,
      hsl(300, 100%, 50%) 25%,
      hsl(0, 100%, 50%) 50%,
      hsl(60, 100%, 50%) 75%,
      hsl(120, 100%, 50%) 100%);
    background-size: 400% 400%;
    animation: holographic 3s ease infinite;
  }

/* ===== ADVANCED KEYFRAME ANIMATIONS ===== */
@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

@keyframes gradient-rotate {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes holographic {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
}

@keyframes float-advanced {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes pulse-glow-advanced {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 0 40px rgba(59, 130, 246, 0.6),
      0 0 80px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }
}

@keyframes shimmer-advanced {
  0% {
    background-position: -200% 0;
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    background-position: 200% 0;
    opacity: 0;
  }
}

@keyframes morph {
  0%, 100% { border-radius: 20px; }
  25% { border-radius: 50px 20px; }
  50% { border-radius: 20px 50px; }
  75% { border-radius: 50px; }
}

  /* ===== ADVANCED INTERACTIVE COMPONENTS ===== */
  .hover-lift-premium {
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
  }

  .hover-lift-premium:hover {
    transform: translateY(-12px) translateZ(20px) rotateX(5deg);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 12px 24px rgba(0, 0, 0, 0.1),
      0 6px 12px rgba(0, 0, 0, 0.05);
  }

  .hover-tilt {
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    transform-style: preserve-3d;
  }

  .hover-tilt:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg) translateZ(20px);
  }

  .magnetic-hover {
    transition: all var(--transition-fast) cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .magnetic-hover:hover {
    transform: scale(1.05) translateZ(10px);
  }

  .glow-advanced {
    animation: pulse-glow-advanced 3s ease-in-out infinite;
  }

  .shimmer-premium {
    position: relative;
    overflow: hidden;
  }

  .shimmer-premium::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent);
    background-size: 200% 100%;
    animation: shimmer-advanced 3s infinite;
  }

  .floating-advanced {
    animation: float-advanced 8s ease-in-out infinite;
  }

  .morphing {
    animation: morph 6s ease-in-out infinite;
  }

  /* ===== ADVANCED BUTTON SYSTEM ===== */
  .btn-neo-premium {
    background: linear-gradient(145deg, #ffffff, #e6e6e6);
    border: 4px solid #000;
    box-shadow: 8px 8px 0px #000;
    color: #000;
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 8px;
    transition: all var(--transition-fast) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .btn-neo-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left var(--transition-normal);
  }

  .btn-neo-premium:hover::before {
    left: 100%;
  }

  .btn-neo-premium:hover {
    box-shadow: 12px 12px 0px #000;
    transform: translate(-4px, -4px);
  }

  .btn-neo-premium:active {
    box-shadow: 4px 4px 0px #000;
    transform: translate(4px, 4px);
  }

  .btn-glass-premium {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    color: inherit;
    padding: 1rem 2rem;
    border-radius: 12px;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }

  .btn-glass-premium::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  }

  .btn-glass-premium:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  }

  .btn-holographic {
    background: linear-gradient(45deg,
      hsl(240, 100%, 50%) 0%,
      hsl(300, 100%, 50%) 25%,
      hsl(0, 100%, 50%) 50%,
      hsl(60, 100%, 50%) 75%,
      hsl(120, 100%, 50%) 100%);
    background-size: 400% 400%;
    animation: holographic 3s ease infinite;
    border: none;
    color: white;
    font-weight: 700;
    padding: 1rem 2rem;
    border-radius: 8px;
    transition: all var(--transition-normal);
  }

  .btn-holographic:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  /* ===== ADVANCED CARD SYSTEM ===== */
  .card-premium {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    -webkit-backdrop-filter: blur(var(--glass-blur)) saturate(180%);
    border-radius: 16px;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .card-premium::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  }

  .card-premium:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 8px 16px rgba(0, 0, 0, 0.05);
  }

  .card-neo-brutal {
    background: #fff;
    border: 4px solid #000;
    box-shadow: 8px 8px 0px #000;
    border-radius: 12px;
    transition: all var(--transition-fast);
  }

  .card-neo-brutal:hover {
    box-shadow: 12px 12px 0px #000;
    transform: translate(-4px, -4px);
  }

  /* ===== ADVANCED TYPOGRAPHY SYSTEM ===== */
  .text-premium {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    letter-spacing: -0.025em;
  }

  .text-shadow-premium {
    text-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .text-glow {
    text-shadow:
      0 0 10px currentColor,
      0 0 20px currentColor,
      0 0 30px currentColor;
  }

  /* ===== RESPONSIVE DESIGN SYSTEM ===== */
  @media (max-width: 768px) {
    .glass-ultra,
    .glass-card-premium,
    .glass-morphism-advanced {
      backdrop-filter: blur(12px) saturate(150%);
      -webkit-backdrop-filter: blur(12px) saturate(150%);
    }

    .hover-lift-premium:hover {
      transform: translateY(-6px);
    }

    .btn-neo-premium {
      padding: 0.75rem 1.5rem;
    }

    .card-premium:hover {
      transform: translateY(-4px) scale(1.01);
    }
  }

  @media (max-width: 480px) {
    .neo-brutal-premium,
    .card-neo-brutal {
      border-width: 3px;
      box-shadow: 6px 6px 0px #000;
    }

    .neo-brutal-premium:hover,
    .card-neo-brutal:hover {
      box-shadow: 8px 8px 0px #000;
      transform: translate(-2px, -2px);
    }
  }

  /* ===== ACCESSIBILITY ENHANCEMENTS ===== */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .floating-advanced,
    .morphing,
    .glow-advanced,
    .shimmer-premium::after {
      animation: none;
    }
  }

  @media (prefers-contrast: high) {
    .glass-ultra,
    .glass-card-premium,
    .glass-morphism-advanced {
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid #000;
    }

    .btn-glass-premium {
      background: rgba(255, 255, 255, 0.9);
      border: 2px solid #000;
      color: #000;
    }
  }

  /* ===== FOCUS STATES FOR ACCESSIBILITY ===== */
  .btn-neo-premium:focus-visible,
  .btn-glass-premium:focus-visible,
  .btn-holographic:focus-visible {
    outline: 3px solid #0066cc;
    outline-offset: 2px;
  }

  .card-premium:focus-visible,
  .card-neo-brutal:focus-visible {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
  }

  /* ===== DARK MODE ENHANCEMENTS ===== */
  .dark .neo-brutal-premium {
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.9);
    box-shadow: var(--neo-shadow-primary);
  }

  .dark .card-neo-brutal {
    background: #1a1a1a;
    border-color: rgba(255, 255, 255, 0.9);
  }

  .dark .btn-neo-premium {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    color: #fff;
    border-color: rgba(255, 255, 255, 0.9);
  }