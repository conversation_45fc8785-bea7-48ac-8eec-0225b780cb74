"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[327],{1366:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},1539:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},1788:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1976:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2085:(t,e,i)=>{i.d(e,{F:()=>o});var r=i(2596);let n=t=>"boolean"==typeof t?`${t}`:0===t?"0":t,s=r.$,o=(t,e)=>i=>{var r;if((null==e?void 0:e.variants)==null)return s(t,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:o,defaultVariants:a}=e,l=Object.keys(o).map(t=>{let e=null==i?void 0:i[t],r=null==a?void 0:a[t];if(null===e)return null;let s=n(e)||n(r);return o[t][s]}),h=i&&Object.entries(i).reduce((t,e)=>{let[i,r]=e;return void 0===r||(t[i]=r),t},{});return s(t,l,null==e||null==(r=e.compoundVariants)?void 0:r.reduce((t,e)=>{let{class:i,className:r,...n}=e;return Object.entries(n).every(t=>{let[e,i]=t;return Array.isArray(i)?i.includes({...a,...h}[e]):({...a,...h})[e]===i})?[...t,i,r]:t},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},2098:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},2486:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},2596:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}(t))&&(r&&(r+=" "),r+=e);return r}},2775:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},2894:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2895:(t,e,i)=>{i.d(e,{A:()=>s});var r=i(2115),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(t,e)=>{let i=(0,r.forwardRef)((i,s)=>{let{color:o="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:h,className:u="",children:d,...c}=i;return(0,r.createElement)("svg",{ref:s,...n,width:a,height:a,stroke:o,strokeWidth:h?24*Number(l)/Number(a):l,className:["lucide","lucide-".concat(t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim()),u].join(" "),...c},[...e.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(d)?d:[d]])});return i.displayName="".concat(t),i}},2915:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2984:(t,e,i)=>{let r;i.d(e,{P:()=>su});var n=i(2115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(s),a=t=>180*t/Math.PI,l=t=>u(a(Math.atan2(t[1],t[0]))),h={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:l,rotateZ:l,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},u=t=>((t%=360)<0&&(t+=360),t),d=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),c=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),p={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:d,scaleY:c,scale:t=>(d(t)+c(t))/2,rotateX:t=>u(a(Math.atan2(t[6],t[5]))),rotateY:t=>u(a(Math.atan2(-t[2],t[0]))),rotateZ:l,rotate:l,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function m(t){return+!!t.includes("scale")}function f(t,e){let i,r;if(!t||"none"===t)return m(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=p,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=h,r=e}if(!r)return m(e);let s=i[e],o=r[1].split(",").map(y);return"function"==typeof s?s(o):o[s]}function y(t){return parseFloat(t.trim())}let g=t=>e=>"string"==typeof e&&e.startsWith(t),v=g("--"),b=g("var(--"),x=t=>!!b(t)&&w.test(t.split("/*")[0].trim()),w=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu;function k({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}let T=(t,e,i)=>t+(e-t)*i;function A(t){return void 0===t||1===t}function P({scale:t,scaleX:e,scaleY:i}){return!A(t)||!A(e)||!A(i)}function S(t){return P(t)||M(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function M(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function E(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function C(t,e=0,i=1,r,n){t.min=E(t.min,e,i,r,n),t.max=E(t.max,e,i,r,n)}function V(t,{x:e,y:i}){C(t.x,e.translate,e.scale,e.originPoint),C(t.y,i.translate,i.scale,i.originPoint)}function D(t,e){t.min=t.min+e,t.max=t.max+e}function j(t,e,i,r,n=.5){let s=T(t.min,t.max,n);C(t,e,i,s,r)}function R(t,e){j(t.x,e.x,e.scaleX,e.scale,e.originX),j(t.y,e.y,e.scaleY,e.scale,e.originY)}function L(t,e){return k(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let F=new Set(["width","height","top","left","right","bottom",...s]),B=(t,e,i)=>i>e?e:i<t?t:i,O={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},I={...O,transform:t=>B(0,1,t)},$={...O,default:1},z=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),N=z("deg"),U=z("%"),W=z("px"),q=z("vh"),H=z("vw"),Y={...U,parse:t=>U.parse(t)/100,transform:t=>U.transform(100*t)},X=t=>e=>e.test(t),G=[O,W,U,N,H,q,{test:t=>"auto"===t,parse:t=>t}],K=t=>G.find(X(t)),_=()=>{},Z=()=>{},J=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tt=t=>t===O||t===W,te=new Set(["x","y","z"]),ti=s.filter(t=>!te.has(t)),tr={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>f(e,"x"),y:(t,{transform:e})=>f(e,"y")};tr.translateX=tr.x,tr.translateY=tr.y;let tn=t=>t,ts={},to=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ta={value:null,addProjectionMetrics:null};function tl(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=to.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){o.has(e)&&(u.schedule(e),t()),l++,e(a)}let u={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(h),e&&ta.value&&ta.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,u.process(t))}};return u}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:h,preUpdate:u,update:d,preRender:c,render:p,postRender:m}=o,f=()=>{let s=ts.useManualTiming?n.timestamp:performance.now();i=!1,ts.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),h.process(n),u.process(n),d.process(n),c.process(n),p.process(n),m.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(f))};return{schedule:to.reduce((e,s)=>{let a=o[s];return e[s]=(e,s=!1,o=!1)=>(!i&&(i=!0,r=!0,n.isProcessing||t(f)),a.schedule(e,s,o)),e},{}),cancel:t=>{for(let e=0;e<to.length;e++)o[to[e]].cancel(t)},state:n,steps:o}}let{schedule:th,cancel:tu,state:td,steps:tc}=tl("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:tn,!0),tp=new Set,tm=!1,tf=!1,ty=!1;function tg(){if(tf){let t=Array.from(tp).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ti.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tf=!1,tm=!1,tp.forEach(t=>t.complete(ty)),tp.clear()}function tv(){tp.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tf=!0)})}class tb{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(tp.add(this),tm||(tm=!0,th.read(tv),th.resolveKeyframes(tg))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tp.delete(this)}cancel(){"scheduled"===this.state&&(tp.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tx=t=>/^0[^.\s]+$/u.test(t),tw=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tT=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tA=(t,e)=>i=>!!("string"==typeof i&&tT.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tP=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(tk);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tS={...O,transform:t=>Math.round(B(0,255,t))},tM={test:tA("rgb","red"),parse:tP("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tS.transform(t)+", "+tS.transform(e)+", "+tS.transform(i)+", "+tw(I.transform(r))+")"},tE={test:tA("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tM.transform},tC={test:tA("hsl","hue"),parse:tP("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+U.transform(tw(e))+", "+U.transform(tw(i))+", "+tw(I.transform(r))+")"},tV={test:t=>tM.test(t)||tE.test(t)||tC.test(t),parse:t=>tM.test(t)?tM.parse(t):tC.test(t)?tC.parse(t):tE.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tM.transform(t):tC.transform(t),getAnimatableNone:t=>{let e=tV.parse(t);return e.alpha=0,tV.transform(e)}},tD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tj="number",tR="color",tL=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tF(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tL,t=>(tV.test(t)?(r.color.push(s),n.push(tR),i.push(tV.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tj),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tB(t){return tF(t).values}function tO(t){let{split:e,types:i}=tF(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tj?n+=tw(t[s]):e===tR?n+=tV.transform(t[s]):n+=t[s]}return n}}let tI=t=>"number"==typeof t?0:tV.test(t)?tV.getAnimatableNone(t):t,t$={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(tk)?.length||0)+(t.match(tD)?.length||0)>0},parse:tB,createTransformer:tO,getAnimatableNone:function(t){let e=tB(t);return tO(t)(e.map(tI))}},tz=new Set(["brightness","contrast","saturate","opacity"]);function tN(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(tk)||[];if(!r)return t;let n=i.replace(r,""),s=+!!tz.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let tU=/\b([a-z-]*)\(.*?\)/gu,tW={...t$,getAnimatableNone:t=>{let e=t.match(tU);return e?e.map(tN).join(" "):t}},tq={...O,transform:Math.round},tH={borderWidth:W,borderTopWidth:W,borderRightWidth:W,borderBottomWidth:W,borderLeftWidth:W,borderRadius:W,radius:W,borderTopLeftRadius:W,borderTopRightRadius:W,borderBottomRightRadius:W,borderBottomLeftRadius:W,width:W,maxWidth:W,height:W,maxHeight:W,top:W,right:W,bottom:W,left:W,padding:W,paddingTop:W,paddingRight:W,paddingBottom:W,paddingLeft:W,margin:W,marginTop:W,marginRight:W,marginBottom:W,marginLeft:W,backgroundPositionX:W,backgroundPositionY:W,rotate:N,rotateX:N,rotateY:N,rotateZ:N,scale:$,scaleX:$,scaleY:$,scaleZ:$,skew:N,skewX:N,skewY:N,distance:W,translateX:W,translateY:W,translateZ:W,x:W,y:W,z:W,perspective:W,transformPerspective:W,opacity:I,originX:Y,originY:Y,originZ:W,zIndex:tq,fillOpacity:I,strokeOpacity:I,numOctaves:tq},tY={...tH,color:tV,backgroundColor:tV,outlineColor:tV,fill:tV,stroke:tV,borderColor:tV,borderTopColor:tV,borderRightColor:tV,borderBottomColor:tV,borderLeftColor:tV,filter:tW,WebkitFilter:tW},tX=t=>tY[t];function tG(t,e){let i=tX(t);return i!==tW&&(i=t$),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tK=new Set(["auto","none","0"]);class t_ extends tb{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&x(r=r.trim())){let n=function t(e,i,r=1){Z(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[n,s]=function(t){let e=Q.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return J(t)?parseFloat(t):t}return x(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!F.has(i)||2!==t.length)return;let[r,n]=t,s=K(r),o=K(n);if(s!==o)if(tt(s)&&tt(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||tx(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!tK.has(e)&&tF(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=tG(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=tr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let tZ=t=>!!(t&&t.getVelocity);function tJ(){r=void 0}let tQ={now:()=>(void 0===r&&tQ.set(td.isProcessing||ts.useManualTiming?td.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(tJ)}};function t0(t,e){-1===t.indexOf(e)&&t.push(e)}function t1(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class t2{constructor(){this.subscriptions=[]}add(t){return t0(this.subscriptions,t),()=>t1(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let t5={current:void 0};class t3{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=tQ.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=tQ.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new t2);let i=this.events[t].add(e);return"change"===t?()=>{i(),th.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return t5.current&&t5.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=tQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function t9(t,e){return new t3(t,e)}let t4=[...G,tV,t$],{schedule:t8}=tl(queueMicrotask,!1),t6={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},t7={};for(let t in t6)t7[t]={isEnabled:e=>t6[t].some(t=>!!e[t])};let et=()=>({translate:0,scale:1,origin:0,originPoint:0}),ee=()=>({x:et(),y:et()}),ei=()=>({min:0,max:0}),er=()=>({x:ei(),y:ei()}),en="undefined"!=typeof window,es={current:null},eo={current:!1},ea=new WeakMap;function el(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function eh(t){return"string"==typeof t||Array.isArray(t)}let eu=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ed=["initial",...eu];function ec(t){return el(t.animate)||ed.some(e=>eh(t[e]))}function ep(t){return!!(ec(t)||t.variants)}function em(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function ef(t,e,i,r){if("function"==typeof e){let[n,s]=em(r);e=e(void 0!==i?i:t.custom,n,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,s]=em(r);e=e(void 0!==i?i:t.custom,n,s)}return e}let ey=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class eg{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tb,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tQ.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,th.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=ec(e),this.isVariantNode=ep(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==a[t]&&tZ(e)&&e.set(a[t],!1)}}mount(t){this.current=t,ea.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),eo.current||function(){if(eo.current=!0,en)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>es.current=t.matches;t.addEventListener("change",e),e()}else es.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||es.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),tu(this.notifyUpdate),tu(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=o.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&th.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in t7){let e=t7[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):er()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<ey.length;e++){let i=ey[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(tZ(n))t.addValue(r,n);else if(tZ(s))t.addValue(r,t9(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,t9(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=t9(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(J(i)||tx(i)))i=parseFloat(i);else{let r;r=i,!t4.find(X(r))&&t$.test(e)&&(i=tG(t,e))}this.setBaseTarget(t,tZ(i)?i.get():i)}return tZ(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=ef(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||tZ(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new t2),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){t8.render(this.render)}}class ev extends eg{constructor(){super(...arguments),this.KeyframeResolver=t_}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tZ(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let eb=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ex={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ew=s.length;function ek(t,e,i){let{style:r,vars:n,transformOrigin:a}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(o.has(t)){l=!0;continue}if(v(t)){n[t]=i;continue}{let e=eb(i,tH[t]);t.startsWith("origin")?(h=!0,a[t]=e):r[t]=e}}if(!e.transform&&(l||i?r.transform=function(t,e,i){let r="",n=!0;for(let o=0;o<ew;o++){let a=s[o],l=t[a];if(void 0===l)continue;let h=!0;if(!(h="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=eb(l,tH[a]);if(!h){n=!1;let e=ex[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;r.transformOrigin=`${t} ${e} ${i}`}}function eT(t,{style:e,vars:i},r,n){let s,o=t.style;for(s in e)o[s]=e[s];for(s in n?.applyProjectionStyles(o,r),i)o.setProperty(s,i[s])}let eA={};function eP(t,{layout:e,layoutId:i}){return o.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eA[t]||"opacity"===t)}function eS(t,e,i){let{style:r}=t,n={};for(let s in r)(tZ(r[s])||e.style&&tZ(e.style[s])||eP(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}class eM extends ev{constructor(){super(...arguments),this.type="html",this.renderInstance=eT}readValueFromInstance(t,e){if(o.has(e))return this.projection?.isProjecting?m(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return f(i,e)})(t,e);{let i=window.getComputedStyle(t),r=(v(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return L(t,e)}build(t,e,i){ek(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return eS(t,e,i)}}let eE=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),eC={offset:"stroke-dashoffset",array:"stroke-dasharray"},eV={offset:"strokeDashoffset",array:"strokeDasharray"};function eD(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,h,u){if(ek(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?eC:eV;t[s.offset]=W.transform(-r);let o=W.transform(e),a=W.transform(i);t[s.array]=`${o} ${a}`}(d,n,s,o,!1)}let ej=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),eR=t=>"string"==typeof t&&"svg"===t.toLowerCase();function eL(t,e,i){let r=eS(t,e,i);for(let i in t)(tZ(t[i])||tZ(e[i]))&&(r[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}class eF extends ev{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=er}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(o.has(e)){let t=tX(e);return t&&t.default||0}return e=ej.has(e)?e:eE(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return eL(t,e,i)}build(t,e,i){eD(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in eT(t,e,void 0,r),e.attrs)t.setAttribute(ej.has(i)?i:eE(i),e.attrs[i])}mount(t){this.isSVGTag=eR(t.tagName),super.mount(t)}}let eB=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eO(t){if("string"!=typeof t||t.includes("-"));else if(eB.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var eI=i(5155);let e$=(0,n.createContext)({}),ez=(0,n.createContext)({strict:!1}),eN=(0,n.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),eU=(0,n.createContext)({});function eW(t){return Array.isArray(t)?t.join(" "):t}let eq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function eH(t,e,i){for(let r in e)tZ(e[r])||eP(r,i)||(t[r]=e[r])}let eY=()=>({...eq(),attrs:{}}),eX=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function eG(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||eX.has(t)}let eK=t=>!eG(t);try{!function(t){"function"==typeof t&&(eK=e=>e.startsWith("on")?!eG(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let e_=(0,n.createContext)(null);function eZ(t){return tZ(t)?t.get():t}let eJ=t=>(e,i)=>{let r=(0,n.useContext)(eU),s=(0,n.useContext)(e_),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,n){return{latestValues:function(t,e,i,r){let n={},s=r(t,{});for(let t in s)n[t]=eZ(s[t]);let{initial:o,animate:a}=t,l=ec(t),h=ep(t);e&&h&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let u=!!i&&!1===i.initial,d=(u=u||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!el(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let r=ef(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let e in t)n[e]=t[e]}}}return n}(i,r,n,t),renderState:e()}})(t,e,r,s);return i?o():function(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)},eQ=eJ({scrapeMotionValuesFromProps:eS,createRenderState:eq}),e0=eJ({scrapeMotionValuesFromProps:eL,createRenderState:eY}),e1=Symbol.for("motionComponentSymbol");function e2(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let e5="data-"+eE("framerAppearId"),e3=(0,n.createContext)({}),e9=en?n.useLayoutEffect:n.useEffect;function e4(t){var e,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)t7[e]={...t7[e],...t[e]}}(s);let a=eO(t)?e0:eQ;function l(e,i){var s;let l,h={...(0,n.useContext)(eN),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,n.useContext)(e$).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:u}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(ec(t)){let{initial:e,animate:i}=t;return{initial:!1===e||eh(e)?e:void 0,animate:eh(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(eU));return(0,n.useMemo)(()=>({initial:e,animate:i}),[eW(e),eW(i)])}(e),c=a(e,u);if(!u&&en){(0,n.useContext)(ez).strict;let e=function(t){let{drag:e,layout:i}=t7;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);l=e.MeasureLayout,d.visualElement=function(t,e,i,r,s){let{visualElement:o}=(0,n.useContext)(eU),a=(0,n.useContext)(ez),l=(0,n.useContext)(e_),h=(0,n.useContext)(eN).reducedMotion,u=(0,n.useRef)(null);r=r||a.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let d=u.current,c=(0,n.useContext)(e3);d&&!d.projection&&s&&("html"===d.type||"svg"===d.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&e2(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,s,c);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[e5],f=(0,n.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return e9(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,n.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),d}(t,c,h,o,e.ProjectionNode)}return(0,eI.jsxs)(eU.Provider,{value:d,children:[l&&d.visualElement?(0,eI.jsx)(l,{visualElement:d.visualElement,...h}):null,function(t,e,i,{latestValues:r},s,o=!1){let a=(eO(t)?function(t,e,i,r){let s=(0,n.useMemo)(()=>{let i=eY();return eD(i,e,eR(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};eH(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return eH(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let i=eq();return ek(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(e,r,s,t),l=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(eK(n)||!0===i&&eG(n)||!e&&!eG(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(e,"string"==typeof t,o),h=t!==n.Fragment?{...l,...a,ref:i}:{},{children:u}=e,d=(0,n.useMemo)(()=>tZ(u)?u.get():u,[u]);return(0,n.createElement)(t,{...h,children:d})}(t,e,(s=d.visualElement,(0,n.useCallback)(t=>{t&&c.onMount&&c.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):e2(i)&&(i.current=t))},[s])),c,u,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let h=(0,n.forwardRef)(l);return h[e1]=t,h}function e8(t,e,i){let r=t.getProps();return ef(r,e,void 0!==i?i:r.custom,t)}function e6(t,e){return t?.[e]??t?.default??t}let e7=t=>Array.isArray(t);function it(t,e){let i=t.getValue("willChange");if(tZ(i)&&i.add)return i.add(e);if(!i&&ts.WillChange){let i=new ts.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let ie=(t,e)=>i=>e(t(i)),ii=(...t)=>t.reduce(ie),ir=t=>1e3*t,is={layout:0,mainThread:0,waapi:0};function io(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ia(t,e){return i=>i>0?e:t}let il=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},ih=[tE,tM,tC];function iu(t){let e=ih.find(e=>e.test(t));if(_(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tC&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=io(a,r,t+1/3),s=io(a,r,t),o=io(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let id=(t,e)=>{let i=iu(t),r=iu(e);if(!i||!r)return ia(t,e);let n={...i};return t=>(n.red=il(i.red,r.red,t),n.green=il(i.green,r.green,t),n.blue=il(i.blue,r.blue,t),n.alpha=T(i.alpha,r.alpha,t),tM.transform(n))},ic=new Set(["none","hidden"]);function ip(t,e){return i=>T(t,e,i)}function im(t){return"number"==typeof t?ip:"string"==typeof t?x(t)?ia:tV.test(t)?id:iv:Array.isArray(t)?iy:"object"==typeof t?tV.test(t)?id:ig:ia}function iy(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>im(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function ig(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=im(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let iv=(t,e)=>{let i=t$.createTransformer(e),r=tF(t),n=tF(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?ic.has(t)&&!n.values.length||ic.has(e)&&!r.values.length?function(t,e){return ic.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):ii(iy(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):(_(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),ia(t,e))};function ib(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?T(t,e,i):im(t)(t,e)}let ix=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>th.update(e,t),stop:()=>tu(e),now:()=>td.isProcessing?td.timestamp:tQ.now()}},iw=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${r.substring(0,r.length-2)})`};function ik(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function iT(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let iA={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iP(t,e){return t*Math.sqrt(1-e*e)}let iS=["duration","bounce"],iM=["stiffness","damping","mass"];function iE(t,e){return e.some(e=>void 0!==t[e])}function iC(t=iA.visualDuration,e=iA.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:iA.velocity,stiffness:iA.stiffness,damping:iA.damping,mass:iA.mass,isResolvedFromDuration:!1,...t};if(!iE(t,iM)&&iE(t,iS))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*B(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:iA.mass,stiffness:r,damping:n}}else{let i=function({duration:t=iA.duration,bounce:e=iA.bounce,velocity:i=iA.velocity,mass:r=iA.mass}){let n,s;_(t<=ir(iA.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let o=1-e;o=B(iA.minDamping,iA.maxDamping,o),t=B(iA.minDuration,iA.maxDuration,t/1e3),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/iP(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=iP(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=ir(t),isNaN(a))return{stiffness:iA.stiffness,damping:iA.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:iA.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-((r.velocity||0)/1e3)}),f=p||0,y=u/(2*Math.sqrt(h*d)),g=a-o,v=Math.sqrt(h/d)/1e3,b=5>Math.abs(g);if(n||(n=b?iA.restSpeed.granular:iA.restSpeed.default),s||(s=b?iA.restDelta.granular:iA.restDelta.default),y<1){let t=iP(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),r=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(r)+t*g*Math.cosh(r))/t}}let x={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let r=0===t?f:0;y<1&&(r=0===t?ir(f):iT(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(ik(x),2e4),e=iw(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function iV({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,y=p+f,g=void 0===o?y:o(y);g!==y&&(f=g-p);let v=t=>-f*Math.exp(-t/r),b=t=>g+v(t),x=t=>{let e=v(t),i=b(t);m.done=Math.abs(e)<=h,m.value=m.done?g:i},w=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;d=t,c=iC({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:iT(b,t,m.value),damping:n,stiffness:s,restDelta:h,restSpeed:u})}};return w(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,x(t),w(t)),void 0!==d&&t>=d)?c.next(t-d):(e||x(t),m)}}}iC.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(ik(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:n/1e3}}(t,100,iC);return t.ease=e.ease,t.duration=ir(e.duration),t.type="keyframes",t};let iD=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function ij(t,e,i,r){return t===e&&i===r?tn:n=>0===n||1===n?n:iD(function(t,e,i,r,n){let s,o,a=0;do(s=iD(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o}(n,0,1,t,i),e,r)}let iR=ij(.42,0,1,1),iL=ij(0,0,.58,1),iF=ij(.42,0,.58,1),iB=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,iO=t=>e=>1-t(1-e),iI=ij(.33,1.53,.69,.99),i$=iO(iI),iz=iB(i$),iN=t=>(t*=2)<1?.5*i$(t):.5*(2-Math.pow(2,-10*(t-1))),iU=t=>1-Math.sin(Math.acos(t)),iW=iO(iU),iq=iB(iU),iH=t=>Array.isArray(t)&&"number"==typeof t[0],iY={linear:tn,easeIn:iR,easeInOut:iF,easeOut:iL,circIn:iU,circInOut:iq,circOut:iW,backIn:i$,backInOut:iz,backOut:iI,anticipate:iN},iX=t=>{if(iH(t)){Z(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,r,n]=t;return ij(e,i,r,n)}return"string"==typeof t?(Z(void 0!==iY[t],`Invalid easing type '${t}'`,"invalid-easing-type"),iY[t]):t},iG=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function iK({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=Array.isArray(r)&&"number"!=typeof r[0]?r.map(iX):iX(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(Z(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||ts.mix||ib,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=ii(Array.isArray(e)?e[i]||tn:e,s)),r.push(s)}return r}(e,r,n),l=a.length,h=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=iG(t[r],t[r+1],i);return a[r](n)};return i?e=>h(B(t[0],t[s-1],e)):h}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=iG(0,e,r);t.push(T(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||iF).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let i_=t=>null!==t;function iZ(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(i_),o=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let iJ={decay:iV,inertia:iV,tween:iK,keyframes:iK,spring:iC};function iQ(t){"string"==typeof t.type&&(t.type=iJ[t.type])}class i0{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let i1=t=>t/100;class i2 extends i0{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==tQ.now()&&this.tick(tQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},is.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;iQ(t);let{type:e=iK,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:o}=t,a=e||iK;a!==iK&&"number"!=typeof o[0]&&(this.mixKeyframes=ii(i1,ib(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=ik(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(u){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(b=s)),v=B(0,1,i)*o}let x=g?{done:!1,value:h[0]}:b.next(v);n&&(x.value=n(x.value));let{done:w}=x;g||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==iV&&(x.value=iZ(h,this.options,f,this.speed)),m&&m(x.value),k&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(t){t=ir(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(tQ.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:t=ix,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(tQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,is.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}function i5(t){let e;return()=>(void 0===e&&(e=t()),e)}let i3=i5(()=>void 0!==window.ScrollTimeline),i9={},i4=function(t,e){let i=i5(t);return()=>i9[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),i8=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,i6={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:i8([0,.65,.55,1]),circOut:i8([.55,0,1,.45]),backIn:i8([.31,.01,.66,-.59]),backOut:i8([.33,1.53,.69,.99])};function i7(t){return"function"==typeof t&&"applyToOptions"in t}class rt extends i0{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,Z("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return i7(t)&&i4()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?i4()?iw(e,i):"ease-out":iH(e)?i8(e):Array.isArray(e)?e.map(e=>t(e,i)||i6.easeOut):i6[e]}(a,n);Array.isArray(d)&&(u.easing=d),ta.value&&is.waapi++;let c={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return ta.value&&p.finished.finally(()=>{is.waapi--}),p}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=iZ(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(t){this.finishedTime=null,this.animation.currentTime=ir(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&i3())?(this.animation.timeline=t,tn):e(this)}}let re={anticipate:iN,backInOut:iz,circInOut:iq};class ri extends rt{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in re&&(t.ease=re[t.ease])}(t),iQ(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new i2({...s,autoplay:!1}),a=ir(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let rr=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t$.test(t)||"0"===t)&&!t.startsWith("url(")),rn=new Set(["opacity","clipPath","filter","transform"]),rs=i5(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class ro extends i0{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tQ.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:h,...u},c=h?.KeyframeResolver||tb;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=tQ.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=rr(n,e),a=rr(s,e);return _(o===a,`You are trying to animate ${e} from "${n}" to "${s}". "${o?s:n}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||i7(i))&&r)}(t,n,s,o)&&((ts.instantAnimations||!a)&&h?.(iZ(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return rs()&&i&&rn.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(u)?new ri({...u,element:u.motionValue.owner.current}):new i2(u);d.finished.then(()=>this.notifyFinished()).catch(tn),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ty=!0,tv(),tg(),ty=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let ra=t=>null!==t,rl={type:"spring",stiffness:500,damping:25,restSpeed:10},rh={type:"keyframes",duration:.8},ru={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},rd=(t,e,i,r={},n,s)=>a=>{let l=e6(r,t)||{},h=l.delay||r.delay||0,{elapsed:u=0}=r;u-=ir(h);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-u,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?rh:o.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:rl:ru)(t,d)),d.duration&&(d.duration=ir(d.duration)),d.repeatDelay&&(d.repeatDelay=ir(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(c=!0)),(ts.instantAnimations||ts.skipAnimations)&&(c=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(ra),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(d.keyframes,l);if(void 0!==t)return void th.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new i2(d):new ro(d)};function rc(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;r&&(s=r);let l=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let r=t.getValue(e,t.latestValues[e]??null),n=a[e];if(void 0===n||h&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(h,e))continue;let o={delay:i,...e6(s||{},e)},u=r.get();if(void 0!==u&&!r.isAnimating&&!Array.isArray(n)&&n===u&&!o.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[e5];if(i){let t=window.MotionHandoffAnimation(i,e,th);null!==t&&(o.startTime=t,d=!0)}}it(t,e),r.start(rd(e,r,n,t.shouldReduceMotion&&F.has(e)?{type:!1}:o,t,d));let c=r.animation;c&&l.push(c)}return o&&Promise.all(l).then(()=>{th.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=e8(t,e)||{};for(let e in n={...n,...i}){var s;let i=e7(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,t9(i))}}(t,o)})}),l}function rp(t,e,i={}){let r=e8(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(rc(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=0,s=1,o){let a=[],l=t.variantChildren.size,h=(l-1)*n,u="function"==typeof r,d=u?t=>r(t,l):1===s?(t=0)=>t*n:(t=0)=>h-t*n;return Array.from(t.variantChildren).sort(rm).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(rp(t,e,{...o,delay:i+(u?0:r)+d(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r,s,o,a,i)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function rm(t,e){return t.sortNodePosition(e)}function rf(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}let ry=ed.length,rg=[...eu].reverse(),rv=eu.length;function rb(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rx(){return{animate:rb(!0),whileInView:rb(),whileHover:rb(),whileTap:rb(),whileDrag:rb(),whileFocus:rb(),exit:rb()}}class rw{constructor(t){this.isMounted=!1,this.node=t}update(){}}class rk extends rw{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>rp(t,e,i)));else if("string"==typeof e)r=rp(t,e,i);else{let n="function"==typeof e?e8(t,e,i.custom):e;r=Promise.all(rc(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=rx(),r=!0,n=e=>(i,r)=>{let n=e8(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ry;t++){let r=ed[t],n=e.props[r];(eh(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},l=[],h=new Set,u={},d=1/0;for(let e=0;e<rv;e++){var c,p;let m=rg[e],f=i[m],y=void 0!==o[m]?o[m]:a[m],g=eh(y),v=m===s?f.isActive:null;!1===v&&(d=e);let b=y===a[m]&&y!==o[m]&&g;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...u},!f.isActive&&null===v||!y&&!f.prevProp||el(y)||"boolean"==typeof y)continue;let x=(c=f.prevProp,"string"==typeof(p=y)?p!==c:!!Array.isArray(p)&&!rf(p,c)),w=x||m===s&&f.isActive&&!b&&g||e>d&&g,k=!1,T=Array.isArray(y)?y:[y],A=T.reduce(n(m),{});!1===v&&(A={});let{prevResolvedValues:P={}}=f,S={...P,...A},M=e=>{w=!0,h.has(e)&&(k=!0,h.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in S){let e=A[t],i=P[t];if(!u.hasOwnProperty(t))(e7(e)&&e7(i)?rf(e,i):e===i)?void 0!==e&&h.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):h.add(t)}f.prevProp=y,f.prevResolvedValues=A,f.isActive&&(u={...u,...A}),r&&t.blockInitialAnimation&&(w=!1);let E=!(b&&x)||k;w&&E&&l.push(...T.map(t=>({animation:t,options:{type:m}})))}if(h.size){let e={};if("boolean"!=typeof o.initial){let i=e8(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),l.push({animation:e})}let m=!!l.length;return r&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(m=!1),r=!1,m?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=s(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=rx(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();el(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let rT=0;class rA extends rw{constructor(){super(...arguments),this.id=rT++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let rP={x:!1,y:!1};function rS(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let rM=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function rE(t){return{point:{x:t.pageX,y:t.pageY}}}function rC(t,e,i,r){return rS(t,e,t=>rM(t)&&i(t,rE(t)),r)}function rV(t){return t.max-t.min}function rD(t,e,i,r=.5){t.origin=r,t.originPoint=T(e.min,e.max,t.origin),t.scale=rV(i)/rV(e),t.translate=T(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function rj(t,e,i,r){rD(t.x,e.x,i.x,r?r.originX:void 0),rD(t.y,e.y,i.y,r?r.originY:void 0)}function rR(t,e,i){t.min=i.min+e.min,t.max=t.min+rV(e)}function rL(t,e,i){t.min=e.min-i.min,t.max=t.min+rV(e)}function rF(t,e,i){rL(t.x,e.x,i.x),rL(t.y,e.y,i.y)}function rB(t){return[t("x"),t("y")]}let rO=({current:t})=>t?t.ownerDocument.defaultView:null,rI=(t,e)=>Math.abs(t-e);class r${constructor(t,e,{transformPagePoint:i,contextWindow:r=window,dragSnapToOrigin:n=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rU(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(rI(t.x,e.x)**2+rI(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=td;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rz(e,this.transformPagePoint),th.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rU("pointercancel"===t.type?this.lastMoveEventInfo:rz(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!rM(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=r||window;let o=rz(rE(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=td;this.history=[{...a,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,rU(o,this.history)),this.removeListeners=ii(rC(this.contextWindow,"pointermove",this.handlePointerMove),rC(this.contextWindow,"pointerup",this.handlePointerUp),rC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),tu(this.updatePoint)}}function rz(t,e){return e?{point:e(t.point)}:t}function rN(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rU({point:t},e){return{point:t,delta:rN(t,rW(e)),offset:rN(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=rW(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>ir(.1)));)i--;if(!r)return{x:0,y:0};let s=(n.timestamp-r.timestamp)/1e3;if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function rW(t){return t[t.length-1]}function rq(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function rH(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function rY(t,e,i){return{min:rX(t,e),max:rX(t,i)}}function rX(t,e){return"number"==typeof t?t:t[e]||0}let rG=new WeakMap;class rK{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=er(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new r$(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(rE(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(rP[t])return null;else return rP[t]=!0,()=>{rP[t]=!1};return rP.x||rP.y?null:(rP.x=rP.y=!0,()=>{rP.x=rP.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),rB(t=>{let e=this.getAxisMotionValue(t).get()||0;if(U.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=rV(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&th.postRender(()=>n(t,e)),it(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>rB(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,distanceThreshold:i,contextWindow:rO(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,r=e||this.latestPanInfo,n=this.isDragging;if(this.cancel(),!n||!r||!i)return;let{velocity:s}=r;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&th.postRender(()=>o(i,r))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!r_(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?T(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?T(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&e2(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:rq(t.x,i,n),y:rq(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:rY(t,"left","right"),y:rY(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&rB(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!e2(e))return!1;let r=e.current;Z(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=L(t,i),{scroll:n}=e;return n&&(D(r.x,n.offset.x),D(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:rH(t.x,s.x),y:rH(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=k(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(rB(o=>{if(!r_(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return it(this.visualElement,t),i.start(rd(t,i,0,e,this.visualElement,!1))}stopAnimation(){rB(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){rB(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){rB(e=>{let{drag:i}=this.getProps();if(!r_(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-T(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!e2(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};rB(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=rV(t),n=rV(e);return n>r?i=iG(e.min,e.max-r,t.min):r>n&&(i=iG(t.min,t.max-n,e.min)),B(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),rB(e=>{if(!r_(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(T(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;rG.set(this.visualElement,this);let t=rC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();e2(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),th.read(e);let n=rS(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(rB(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function r_(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class rZ extends rw{constructor(t){super(t),this.removeGroupControls=tn,this.removeListeners=tn,this.controls=new rK(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tn}unmount(){this.removeGroupControls(),this.removeListeners()}}let rJ=t=>(e,i)=>{t&&th.postRender(()=>t(e,i))};class rQ extends rw{constructor(){super(...arguments),this.removePointerDownListener=tn}onPointerDown(t){this.session=new r$(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:rO(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:rJ(t),onStart:rJ(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&th.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=rC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let r0={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function r1(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let r2={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!W.test(t))return t;else t=parseFloat(t);let i=r1(t,e.target.x),r=r1(t,e.target.y);return`${i}% ${r}%`}},r5=!1;class r3 extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in r4)eA[t]=r4[t],v(t)&&(eA[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),r5&&n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),r0.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r5=!0,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||th.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),t8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function r9(t){let[e,i]=function(t=!0){let e=(0,n.useContext)(e_);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=e,o=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return s(o)},[t]);let a=(0,n.useCallback)(()=>t&&r&&r(o),[o,r,t]);return!i&&r?[!1,a]:[!0]}(),r=(0,n.useContext)(e$);return(0,eI.jsx)(r3,{...t,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(e3),isPresent:e,safeToRemove:i})}let r4={borderRadius:{...r2,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:r2,borderTopRightRadius:r2,borderBottomLeftRadius:r2,borderBottomRightRadius:r2,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=t$.parse(t);if(r.length>5)return t;let n=t$.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=T(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};function r8(t){return"object"==typeof t&&null!==t}function r6(t){return r8(t)&&"ownerSVGElement"in t}let r7=(t,e)=>t.depth-e.depth;class nt{constructor(){this.children=[],this.isDirty=!1}add(t){t0(this.children,t),this.isDirty=!0}remove(t){t1(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(r7),this.isDirty=!1,this.children.forEach(t)}}let ne=["TopLeft","TopRight","BottomLeft","BottomRight"],ni=ne.length,nr=t=>"string"==typeof t?parseFloat(t):t,nn=t=>"number"==typeof t||W.test(t);function ns(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let no=nl(0,.5,iW),na=nl(.5,.95,tn);function nl(t,e,i){return r=>r<t?0:r>e?1:i(iG(t,e,r))}function nh(t,e){t.min=e.min,t.max=e.max}function nu(t,e){nh(t.x,e.x),nh(t.y,e.y)}function nd(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nc(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function np(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(U.test(e)&&(e=parseFloat(e),e=T(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=T(s.min,s.max,r);t===s&&(a-=e),t.min=nc(t.min,e,i,a,n),t.max=nc(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let nm=["x","scaleX","originX"],nf=["y","scaleY","originY"];function ny(t,e,i,r){np(t.x,e,nm,i?i.x:void 0,r?r.x:void 0),np(t.y,e,nf,i?i.y:void 0,r?r.y:void 0)}function ng(t){return 0===t.translate&&1===t.scale}function nv(t){return ng(t.x)&&ng(t.y)}function nb(t,e){return t.min===e.min&&t.max===e.max}function nx(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nw(t,e){return nx(t.x,e.x)&&nx(t.y,e.y)}function nk(t){return rV(t.x)/rV(t.y)}function nT(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nA{constructor(){this.members=[]}add(t){t0(this.members,t),t.scheduleRender()}remove(t){if(t1(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nP={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nS=["","X","Y","Z"],nM=0;function nE(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function nC({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=nM++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ta.value&&(nP.nodes=nP.calculatedTargetDeltas=nP.calculatedProjections=0),this.nodes.forEach(nj),this.nodes.forEach(n$),this.nodes.forEach(nz),this.nodes.forEach(nR),ta.addProjectionMetrics&&ta.addProjectionMetrics(nP)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nt)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new t2),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=r6(e)&&!(r6(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=0,n=()=>this.root.updateBlockedByResize=!1;th.read(()=>{r=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==r&&(r=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tQ.now(),r=({timestamp:e})=>{let n=e-i;n>=250&&(tu(r),t(n-250))};return th.setup(r,!0),()=>tu(r)}(n,250),r0.hasAnimatedSinceResize&&(r0.hasAnimatedSinceResize=!1,this.nodes.forEach(nI)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||nY,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!nw(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...e6(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||nI(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),tu(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nN),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[e5];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",th,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nF);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nB);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nO),this.nodes.forEach(nV),this.nodes.forEach(nD)):this.nodes.forEach(nB),this.clearAllSnapshots();let t=tQ.now();td.delta=B(0,1e3/60,t-td.timestamp),td.timestamp=t,td.isProcessing=!0,tc.update.process(td),tc.preRender.process(td),tc.render.process(td),td.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,t8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nL),this.sharedNodes.forEach(nU)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,th.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){th.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||rV(this.snapshot.measuredBox.x)||rV(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=er(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nv(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||S(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),nK((e=r).x),nK(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return er();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nZ))){let{scroll:t}=this.root;t&&(D(e.x,t.offset.x),D(e.y,t.offset.y))}return e}removeElementScroll(t){let e=er();if(nu(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&nu(e,t),D(e.x,n.offset.x),D(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=er();nu(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&R(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),S(r.latestValues)&&R(i,r.latestValues)}return S(this.latestValues)&&R(i,this.latestValues),i}removeTransform(t){let e=er();nu(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!S(i.latestValues))continue;P(i.latestValues)&&i.updateSnapshot();let r=er();nu(r,i.measurePageBox()),ny(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return S(this.latestValues)&&ny(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==td.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=td.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rF(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nu(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=er(),this.targetWithTransforms=er()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,rR(s.x,o.x,a.x),rR(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nu(this.target,this.layout.layoutBox),V(this.target,this.targetDelta)):nu(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=er(),this.relativeTargetOrigin=er(),rF(this.relativeTargetOrigin,this.target,t.target),nu(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ta.value&&nP.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||P(this.parent.latestValues)||M(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===td.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;nu(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&R(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,V(t,s)),r&&S(n.latestValues)&&R(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=er());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nd(this.prevProjectionDelta.x,this.projectionDelta.x),nd(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rj(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nT(this.projectionDelta.x,this.prevProjectionDelta.x)&&nT(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),ta.value&&nP.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ee(),this.projectionDelta=ee(),this.projectionDeltaWithTransform=ee()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=ee();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=er(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(nH));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(nW(o.x,t.x,r),nW(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,y;rF(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=r,nq(p.x,m.x,f.x,y),nq(p.y,m.y,f.y,y),i&&(h=this.relativeTarget,c=i,nb(h.x,c.x)&&nb(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=er()),nu(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=T(0,i.opacity??1,no(r)),t.opacityExit=T(e.opacity??1,0,na(r))):s&&(t.opacity=T(e.opacity??1,i.opacity??1,r));for(let n=0;n<ni;n++){let s=`border${ne[n]}Radius`,o=ns(e,s),a=ns(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nn(o)===nn(a)?(t[s]=Math.max(T(nr(o),nr(a),r),0),(U.test(a)||U.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=T(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(tu(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=th.update(()=>{r0.hasAnimatedSinceResize=!0,is.layout++,this.motionValue||(this.motionValue=t9(0)),this.currentAnimation=function(t,e,i){let r=tZ(t)?t:t9(t);return r.start(rd("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{is.layout--},onComplete:()=>{is.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&n_(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||er();let e=rV(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=rV(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}nu(e,i),R(e,n),rj(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nA),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&nE("z",t,r,this.animationValues);for(let e=0;e<nS.length;e++)nE(`rotate${nS[e]}`,t,r,this.animationValues),nE(`skew${nS[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eZ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eZ(e?.pointerEvents)||""),this.hasProjected&&!S(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let n=r.animationValues||r.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n);i&&(s=i(n,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,r.animationValues?t.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:t.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,eA){if(void 0===n[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=eA[e],l="none"===s?n[e]:i(n[e],r);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=r===this?eZ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nF),this.root.sharedNodes.clear()}}}function nV(t){t.updateLayout()}function nD(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?rB(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=rV(r);r.min=i[t].min,r.max=r.min+n}):n_(n,e.layoutBox,i)&&rB(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=rV(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=ee();rj(o,i,e.layoutBox);let a=ee();s?rj(a,t.applyTransform(r,!0),e.measuredBox):rj(a,i,e.layoutBox);let l=!nv(o),h=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=er();rF(o,e.layoutBox,n.layoutBox);let a=er();rF(a,i,s.layoutBox),nw(o,a)||(h=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nj(t){ta.value&&nP.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nR(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nL(t){t.clearSnapshot()}function nF(t){t.clearMeasurements()}function nB(t){t.isLayoutDirty=!1}function nO(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nI(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n$(t){t.resolveTargetDelta()}function nz(t){t.calcProjection()}function nN(t){t.resetSkewAndRotation()}function nU(t){t.removeLeadSnapshot()}function nW(t,e,i){t.translate=T(e.translate,0,i),t.scale=T(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nq(t,e,i,r){t.min=T(e.min,i.min,r),t.max=T(e.max,i.max,r)}function nH(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nY={duration:.45,ease:[.4,0,.1,1]},nX=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nG=nX("applewebkit/")&&!nX("chrome/")?Math.round:tn;function nK(t){t.min=nG(t.min),t.max=nG(t.max)}function n_(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nk(e)-nk(i)))}function nZ(t){return t!==t.root&&t.scroll?.wasRoot}let nJ=nC({attachResizeListener:(t,e)=>rS(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nQ={current:void 0},n0=nC({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nQ.current){let t=new nJ({});t.mount(window),t.setOptions({layoutScroll:!0}),nQ.current=t}return nQ.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n1(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function n2(t){return!("touch"===t.pointerType||rP.x||rP.y)}function n5(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&th.postRender(()=>n(e,rE(e)))}class n3 extends rw{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=n1(t,i),o=t=>{if(!n2(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{n2(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(n5(this.node,e,"Start"),t=>n5(this.node,t,"End"))))}unmount(){}}class n9 extends rw{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ii(rS(this.node.current,"focus",()=>this.onFocus()),rS(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let n4=(t,e)=>!!e&&(t===e||n4(t,e.parentElement)),n8=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),n6=new WeakSet;function n7(t){return e=>{"Enter"===e.key&&t(e)}}function st(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function se(t){return rM(t)&&!(rP.x||rP.y)}function si(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&th.postRender(()=>n(e,rE(e)))}class sr extends rw{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=n1(t,i),o=t=>{let r=t.currentTarget;if(!se(t))return;n6.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),n6.has(r)&&n6.delete(r),se(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||n4(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),r8(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let r=n7(()=>{if(n6.has(i))return;st(i,"down");let t=n7(()=>{st(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>st(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)})(t,n)),n8.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(si(this.node,e,"Start"),(t,{success:e})=>si(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sn=new WeakMap,ss=new WeakMap,so=t=>{let e=sn.get(t.target);e&&e(t)},sa=t=>{t.forEach(so)},sl={some:0,all:1};class sh extends rw{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:sl[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;ss.has(i)||ss.set(i,{});let r=ss.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(sa,{root:t,...e})),r[n]}(e);return sn.set(t,i),r.observe(t),()=>{sn.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let su=function(t,e){if("undefined"==typeof Proxy)return e4;let i=new Map,r=(i,r)=>e4(i,r,t,e);return new Proxy((t,e)=>r(t,e),{get:(n,s)=>"create"===s?r:(i.has(s)||i.set(s,e4(s,void 0,t,e)),i.get(s))})}({animation:{Feature:rk},exit:{Feature:rA},inView:{Feature:sh},tap:{Feature:sr},focus:{Feature:n9},hover:{Feature:n3},pan:{Feature:rQ},drag:{Feature:rZ,ProjectionNode:n0,MeasureLayout:r9},layout:{ProjectionNode:n0,MeasureLayout:r9}},(t,e)=>eO(t)?new eF(e):new eM(e,{allowProjection:t!==n.Fragment}))},3109:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3127:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},3509:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3786:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]])},4213:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4416:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4455:(t,e,i)=>{i.d(e,{D:()=>h,N:()=>u});var r=i(2115);let n=["light","dark"],s="(prefers-color-scheme: dark)",o="undefined"==typeof window,a=(0,r.createContext)(void 0),l={setTheme:t=>{},themes:[]},h=()=>{var t;return null!=(t=(0,r.useContext)(a))?t:l},u=t=>(0,r.useContext)(a)?r.createElement(r.Fragment,null,t.children):r.createElement(c,t),d=["light","dark"],c=({forcedTheme:t,disableTransitionOnChange:e=!1,enableSystem:i=!0,enableColorScheme:o=!0,storageKey:l="theme",themes:h=d,defaultTheme:u=i?"system":"light",attribute:c="data-theme",value:g,children:v,nonce:b})=>{let[x,w]=(0,r.useState)(()=>m(l,u)),[k,T]=(0,r.useState)(()=>m(l)),A=g?Object.values(g):h,P=(0,r.useCallback)(t=>{let r=t;if(!r)return;"system"===t&&i&&(r=y());let s=g?g[r]:r,a=e?f():null,l=document.documentElement;if("class"===c?(l.classList.remove(...A),s&&l.classList.add(s)):s?l.setAttribute(c,s):l.removeAttribute(c),o){let t=n.includes(u)?u:null,e=n.includes(r)?r:t;l.style.colorScheme=e}null==a||a()},[]),S=(0,r.useCallback)(t=>{w(t);try{localStorage.setItem(l,t)}catch(t){}},[t]),M=(0,r.useCallback)(e=>{T(y(e)),"system"===x&&i&&!t&&P("system")},[x,t]);(0,r.useEffect)(()=>{let t=window.matchMedia(s);return t.addListener(M),M(t),()=>t.removeListener(M)},[M]),(0,r.useEffect)(()=>{let t=t=>{t.key===l&&S(t.newValue||u)};return window.addEventListener("storage",t),()=>window.removeEventListener("storage",t)},[S]),(0,r.useEffect)(()=>{P(null!=t?t:x)},[t,x]);let E=(0,r.useMemo)(()=>({theme:x,setTheme:S,forcedTheme:t,resolvedTheme:"system"===x?k:x,themes:i?[...h,"system"]:h,systemTheme:i?k:void 0}),[x,S,t,k,i,h]);return r.createElement(a.Provider,{value:E},r.createElement(p,{forcedTheme:t,disableTransitionOnChange:e,enableSystem:i,enableColorScheme:o,storageKey:l,themes:h,defaultTheme:u,attribute:c,value:g,children:v,attrs:A,nonce:b}),v)},p=(0,r.memo)(({forcedTheme:t,storageKey:e,attribute:i,enableSystem:o,enableColorScheme:a,defaultTheme:l,value:h,attrs:u,nonce:d})=>{let c="system"===l,p="class"===i?`var d=document.documentElement,c=d.classList;c.remove(${u.map(t=>`'${t}'`).join(",")});`:`var d=document.documentElement,n='${i}',s='setAttribute';`,m=a?n.includes(l)&&l?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${l}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",f=(t,e=!1,r=!0)=>{let s=h?h[t]:t,o=e?t+"|| ''":`'${s}'`,l="";return a&&r&&!e&&n.includes(t)&&(l+=`d.style.colorScheme = '${t}';`),"class"===i?l+=e||s?`c.add(${o})`:"null":s&&(l+=`d[s](n,${o})`),l},y=t?`!function(){${p}${f(t)}}()`:o?`!function(){try{${p}var e=localStorage.getItem('${e}');if('system'===e||(!e&&${c})){var t='${s}',m=window.matchMedia(t);if(m.media!==t||m.matches){${f("dark")}}else{${f("light")}}}else if(e){${h?`var x=${JSON.stringify(h)};`:""}${f(h?"x[e]":"e",!0)}}${c?"":"else{"+f(l,!1,!1)+"}"}${m}}catch(e){}}()`:`!function(){try{${p}var e=localStorage.getItem('${e}');if(e){${h?`var x=${JSON.stringify(h)};`:""}${f(h?"x[e]":"e",!0)}}else{${f(l,!1,!1)};}${m}}catch(t){}}();`;return r.createElement("script",{nonce:d,dangerouslySetInnerHTML:{__html:y}})},()=>!0),m=(t,e)=>{let i;if(!o){try{i=localStorage.getItem(t)||void 0}catch(t){}return i||e}},f=()=>{let t=document.createElement("style");return t.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=t=>(t||(t=window.matchMedia(s)),t.matches?"dark":"light")},4516:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4624:(t,e,i)=>{i.d(e,{DX:()=>o});var r=i(2115);function n(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}var s=i(5155),o=function(t){let e=function(t){let e=r.forwardRef((t,e)=>{let{children:i,...s}=t;if(r.isValidElement(i)){var o;let t,a,l=(o=i,(a=(t=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.ref:(a=(t=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?o.props.ref:o.props.ref||o.ref),h=function(t,e){let i={...e};for(let r in e){let n=t[r],s=e[r];/^on[A-Z]/.test(r)?n&&s?i[r]=(...t)=>{let e=s(...t);return n(...t),e}:n&&(i[r]=n):"style"===r?i[r]={...n,...s}:"className"===r&&(i[r]=[n,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==r.Fragment&&(h.ref=e?function(...t){return e=>{let i=!1,r=t.map(t=>{let r=n(t,e);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let e=0;e<r.length;e++){let i=r[e];"function"==typeof i?i():n(t[e],null)}}}}(e,l):l),r.cloneElement(i,h)}return r.Children.count(i)>1?r.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=r.forwardRef((t,i)=>{let{children:n,...o}=t,a=r.Children.toArray(n),h=a.find(l);if(h){let t=h.props.children,n=a.map(e=>e!==h?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:r.isValidElement(t)?r.cloneElement(t,void 0,n):null})}return(0,s.jsx)(e,{...o,ref:i,children:n})});return i.displayName=`${t}.Slot`,i}("Slot"),a=Symbol("radix.slottable");function l(t){return r.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===a}},4783:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},4869:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5487:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},6785:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7580:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7949:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8136:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},8186:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},8832:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},8883:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9037:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9074:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9099:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},9621:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},9688:(t,e,i)=>{i.d(e,{QP:()=>U});let r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},n=/^\[(.+)\]$/,s=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:o(e,t)).classGroupId=i;return}if("function"==typeof t)return a(t)?void s(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,n])=>{s(n,o(e,t),i,r)})})},o=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},a=t=>t.isThemeGetter,l=(t,e)=>e?t.map(([t,i])=>[t,i.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,i])=>[e+t,i])):t)]):t,h=t=>{if(t.length<=1)return t;let e=[],i=[];return t.forEach(t=>{"["===t[0]?(e.push(...i.sort(),t),i=[]):i.push(t)}),e.push(...i.sort()),e},u=/\s+/;function d(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=c(t))&&(r&&(r+=" "),r+=e);return r}let c=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=c(t[r]))&&(i&&(i+=" "),i+=e);return i},p=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},m=/^\[(?:([a-z-]+):)?(.+)\]$/i,f=/^\d+\/\d+$/,y=new Set(["px","full","screen"]),g=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,b=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,x=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,w=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,k=t=>A(t)||y.has(t)||f.test(t),T=t=>O(t,"length",I),A=t=>!!t&&!Number.isNaN(Number(t)),P=t=>O(t,"number",A),S=t=>!!t&&Number.isInteger(Number(t)),M=t=>t.endsWith("%")&&A(t.slice(0,-1)),E=t=>m.test(t),C=t=>g.test(t),V=new Set(["length","size","percentage"]),D=t=>O(t,V,$),j=t=>O(t,"position",$),R=new Set(["image","url"]),L=t=>O(t,R,N),F=t=>O(t,"",z),B=()=>!0,O=(t,e,i)=>{let r=m.exec(t);return!!r&&(r[1]?"string"==typeof e?r[1]===e:e.has(r[1]):i(r[2]))},I=t=>v.test(t)&&!b.test(t),$=()=>!1,z=t=>x.test(t),N=t=>w.test(t);Symbol.toStringTag;let U=function(t,...e){let i,o,a,c=function(h){let u;return o=(i={cache:(t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}})((u=e.reduce((t,e)=>e(t),t())).cacheSize),parseClassName:(t=>{let{separator:e,experimentalParseClassName:i}=t,r=1===e.length,n=e[0],s=e.length,o=t=>{let i,o=[],a=0,l=0;for(let h=0;h<t.length;h++){let u=t[h];if(0===a){if(u===n&&(r||t.slice(h,h+s)===e)){o.push(t.slice(l,h)),l=h+s;continue}if("/"===u){i=h;continue}}"["===u?a++:"]"===u&&a--}let h=0===o.length?t:t.substring(l),u=h.startsWith("!"),d=u?h.substring(1):h;return{modifiers:o,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return i?t=>i({className:t,parseClassName:o}):o})(u),...(t=>{let e=(t=>{let{theme:e,prefix:i}=t,r={nextPart:new Map,validators:[]};return l(Object.entries(t.classGroups),i).forEach(([t,i])=>{s(i,r,t,e)}),r})(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:o}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||(t=>{if(n.test(t)){let e=n.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}})(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&o[t]?[...r,...o[t]]:r}}})(u)}).cache.get,a=i.cache.set,c=p,p(h)};function p(t){let e=o(t);if(e)return e;let r=((t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n}=e,s=[],o=t.trim().split(u),a="";for(let t=o.length-1;t>=0;t-=1){let e=o[t],{modifiers:l,hasImportantModifier:u,baseClassName:d,maybePostfixModifierPosition:c}=i(e),p=!!c,m=r(p?d.substring(0,c):d);if(!m){if(!p||!(m=r(d))){a=e+(a.length>0?" "+a:a);continue}p=!1}let f=h(l).join(":"),y=u?f+"!":f,g=y+m;if(s.includes(g))continue;s.push(g);let v=n(m,p);for(let t=0;t<v.length;++t){let e=v[t];s.push(y+e)}a=e+(a.length>0?" "+a:a)}return a})(t,i);return a(t,r),r}return function(){return c(d.apply(null,arguments))}}(()=>{let t=p("colors"),e=p("spacing"),i=p("blur"),r=p("brightness"),n=p("borderColor"),s=p("borderRadius"),o=p("borderSpacing"),a=p("borderWidth"),l=p("contrast"),h=p("grayscale"),u=p("hueRotate"),d=p("invert"),c=p("gap"),m=p("gradientColorStops"),f=p("gradientColorStopPositions"),y=p("inset"),g=p("margin"),v=p("opacity"),b=p("padding"),x=p("saturate"),w=p("scale"),V=p("sepia"),R=p("skew"),O=p("space"),I=p("translate"),$=()=>["auto","contain","none"],z=()=>["auto","hidden","clip","visible","scroll"],N=()=>["auto",E,e],U=()=>[E,e],W=()=>["",k,T],q=()=>["auto",A,E],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Y=()=>["solid","dashed","dotted","double","none"],X=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],G=()=>["start","end","center","between","around","evenly","stretch"],K=()=>["","0",E],_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>[A,E];return{cacheSize:500,separator:":",theme:{colors:[B],spacing:[k,T],blur:["none","",C,E],brightness:Z(),borderColor:[t],borderRadius:["none","","full",C,E],borderSpacing:U(),borderWidth:W(),contrast:Z(),grayscale:K(),hueRotate:Z(),invert:K(),gap:U(),gradientColorStops:[t],gradientColorStopPositions:[M,T],inset:N(),margin:N(),opacity:Z(),padding:U(),saturate:Z(),scale:Z(),sepia:K(),skew:Z(),space:U(),translate:U()},classGroups:{aspect:[{aspect:["auto","square","video",E]}],container:["container"],columns:[{columns:[C]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),E]}],overflow:[{overflow:z()}],"overflow-x":[{"overflow-x":z()}],"overflow-y":[{"overflow-y":z()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",S,E]}],basis:[{basis:N()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",E]}],grow:[{grow:K()}],shrink:[{shrink:K()}],order:[{order:["first","last","none",S,E]}],"grid-cols":[{"grid-cols":[B]}],"col-start-end":[{col:["auto",{span:["full",S,E]},E]}],"col-start":[{"col-start":q()}],"col-end":[{"col-end":q()}],"grid-rows":[{"grid-rows":[B]}],"row-start-end":[{row:["auto",{span:[S,E]},E]}],"row-start":[{"row-start":q()}],"row-end":[{"row-end":q()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",E]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",E]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...G()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...G(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...G(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[O]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[O]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",E,e]}],"min-w":[{"min-w":[E,e,"min","max","fit"]}],"max-w":[{"max-w":[E,e,"none","full","min","max","fit","prose",{screen:[C]},C]}],h:[{h:[E,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[E,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[E,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[E,e,"auto","min","max","fit"]}],"font-size":[{text:["base",C,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",P]}],"font-family":[{font:[B]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",E]}],"line-clamp":[{"line-clamp":["none",A,P]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",k,E]}],"list-image":[{"list-image":["none",E]}],"list-style-type":[{list:["none","disc","decimal",E]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Y(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",k,T]}],"underline-offset":[{"underline-offset":["auto",k,E]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",E]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",E]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),j]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",D]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},L]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...Y(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:Y()}],"border-color":[{border:[n]}],"border-color-x":[{"border-x":[n]}],"border-color-y":[{"border-y":[n]}],"border-color-s":[{"border-s":[n]}],"border-color-e":[{"border-e":[n]}],"border-color-t":[{"border-t":[n]}],"border-color-r":[{"border-r":[n]}],"border-color-b":[{"border-b":[n]}],"border-color-l":[{"border-l":[n]}],"divide-color":[{divide:[n]}],"outline-style":[{outline:["",...Y()]}],"outline-offset":[{"outline-offset":[k,E]}],"outline-w":[{outline:[k,T]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[k,T]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",C,F]}],"shadow-color":[{shadow:[B]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...X(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":X()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",C,E]}],grayscale:[{grayscale:[h]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[x]}],sepia:[{sepia:[V]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[h]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",E]}],duration:[{duration:Z()}],ease:[{ease:["linear","in","out","in-out",E]}],delay:[{delay:Z()}],animate:[{animate:["none","spin","ping","pulse","bounce",E]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[S,E]}],"translate-x":[{"translate-x":[I]}],"translate-y":[{"translate-y":[I]}],"skew-x":[{"skew-x":[R]}],"skew-y":[{"skew-y":[R]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",E]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",E]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",E]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[k,T,P]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},9881:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(2895).A)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])}}]);