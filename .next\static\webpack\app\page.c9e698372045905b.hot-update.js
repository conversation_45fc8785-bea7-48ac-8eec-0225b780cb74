"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/floating-shapes.tsx":
/*!********************************************!*\
  !*** ./src/components/floating-shapes.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__  auto */ \nvar _s = $RefreshSig$();\n\n\nfunction FloatingShapesContent() {\n    _s();\n    const [particles, setParticles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FloatingShapesContent.useEffect\": ()=>{\n            const generateParticles = {\n                \"FloatingShapesContent.useEffect.generateParticles\": ()=>{\n                    const newParticles = [];\n                    const colors = [\n                        'from-blue-400 to-purple-500',\n                        'from-pink-400 to-red-500',\n                        'from-green-400 to-blue-500',\n                        'from-yellow-400 to-orange-500',\n                        'from-purple-400 to-pink-500',\n                        'from-cyan-400 to-blue-500',\n                        'from-indigo-400 to-purple-500',\n                        'from-emerald-400 to-teal-500'\n                    ];\n                    const shapes = [\n                        'circle',\n                        'triangle',\n                        'square',\n                        'diamond',\n                        'hexagon'\n                    ];\n                    for(let i = 0; i < 12; i++){\n                        newParticles.push({\n                            id: i,\n                            x: Math.random() * 100,\n                            y: Math.random() * 100,\n                            size: Math.random() * 60 + 20,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            duration: Math.random() * 20 + 15,\n                            delay: Math.random() * 5,\n                            shape: shapes[Math.floor(Math.random() * shapes.length)]\n                        });\n                    }\n                    setParticles(newParticles);\n                }\n            }[\"FloatingShapesContent.useEffect.generateParticles\"];\n            generateParticles();\n        }\n    }[\"FloatingShapesContent.useEffect\"], []);\n    const getShapeClipPath = (shape)=>{\n        switch(shape){\n            case 'triangle':\n                return 'polygon(50% 0%, 0% 100%, 100% 100%)';\n            case 'square':\n                return 'polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)';\n            case 'diamond':\n                return 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)';\n            case 'hexagon':\n                return 'polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)';\n            default:\n                return 'none';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 overflow-hidden pointer-events-none z-0\",\n        children: [\n            particles.map((particle)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"absolute bg-gradient-to-r \".concat(particle.color, \" opacity-15 blur-[0.5px]\"),\n                    style: {\n                        left: \"\".concat(particle.x, \"%\"),\n                        top: \"\".concat(particle.y, \"%\"),\n                        width: \"\".concat(particle.size, \"px\"),\n                        height: \"\".concat(particle.size, \"px\"),\n                        clipPath: getShapeClipPath(particle.shape),\n                        borderRadius: particle.shape === 'circle' ? '50%' : '8px'\n                    },\n                    animate: {\n                        y: [\n                            0,\n                            -40,\n                            20,\n                            -30,\n                            0\n                        ],\n                        x: [\n                            0,\n                            30,\n                            -20,\n                            25,\n                            0\n                        ],\n                        rotate: [\n                            0,\n                            180,\n                            270,\n                            360\n                        ],\n                        scale: [\n                            1,\n                            1.2,\n                            0.8,\n                            1.1,\n                            1\n                        ],\n                        opacity: [\n                            0.15,\n                            0.25,\n                            0.1,\n                            0.2,\n                            0.15\n                        ]\n                    },\n                    transition: {\n                        duration: particle.duration,\n                        repeat: Infinity,\n                        ease: \"easeInOut\",\n                        delay: particle.delay\n                    }\n                }, particle.id, false, {\n                    fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/4 left-1/4 w-32 h-32 holographic rounded-full opacity-20 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        -50,\n                        0\n                    ],\n                    x: [\n                        0,\n                        40,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.3,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 25,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-3/4 right-1/4 w-24 h-24 holographic rounded-full opacity-25 blur-sm\",\n                animate: {\n                    y: [\n                        0,\n                        30,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -30,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        0.8,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 20,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 2\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/2 right-1/3 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 morphing\",\n                animate: {\n                    y: [\n                        0,\n                        -25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        25,\n                        0\n                    ],\n                    rotate: [\n                        0,\n                        90,\n                        180,\n                        270,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 30,\n                    repeat: Infinity,\n                    ease: \"linear\"\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute top-1/3 right-1/5 w-16 h-16 glass-ultra rounded-lg opacity-30\",\n                animate: {\n                    y: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    x: [\n                        0,\n                        15,\n                        0\n                    ],\n                    rotateY: [\n                        0,\n                        180,\n                        360\n                    ]\n                },\n                transition: {\n                    duration: 18,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 1\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"absolute bottom-1/3 left-1/5 w-12 h-12 glass-ultra rounded-full opacity-25\",\n                animate: {\n                    y: [\n                        0,\n                        25,\n                        0\n                    ],\n                    x: [\n                        0,\n                        -20,\n                        0\n                    ],\n                    scale: [\n                        1,\n                        1.4,\n                        1\n                    ]\n                },\n                transition: {\n                    duration: 22,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                    delay: 3\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Portfolio_1\\\\src\\\\components\\\\floating-shapes.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(FloatingShapesContent, \"n2oV9J0JxRF0n1eg4nXLNJcP/RY=\");\n_c = FloatingShapesContent;\nvar _c;\n$RefreshReg$(_c, \"FloatingShapesContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/floating-shapes.tsx\n"));

/***/ })

});