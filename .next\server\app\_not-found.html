<!DOCTYPE html><!--l4Q0NMTmhHtPBXa2ZyD85--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/641cf9563d92ca3b.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-bfa3c263a159a400.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-eda38e26c0391a47.js" async=""></script><script src="/_next/static/chunks/main-app-7ab8d74cd128672f.js" async=""></script><script src="/_next/static/chunks/app/layout-f41564b1781e53a9.js" async=""></script><meta name="robots" content="noindex"/><title>404: This page could not be found.</title><title>Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast</title><meta name="description" content="Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies."/><meta name="author" content="Abhishek Kumar Singh"/><meta name="keywords" content="Abhishek Kumar Singh,Frontend Developer,Full Stack Developer,React Developer,Next.js,IIT Roorkee,College of Engineering Roorkee,Computer Science,Web Developer,JavaScript,TypeScript,Tailwind CSS,Portfolio"/><meta name="creator" content="Abhishek Kumar Singh"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="resume-url" content="/resume.pdf"/><meta name="resume-download" content="/api/resume"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast"/><meta property="og:description" content="Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies."/><meta property="og:url" content="https://abhishek-portfolio.vercel.app"/><meta property="og:site_name" content="Abhishek Kumar Singh Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://localhost:3000/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Abhishek Kumar Singh - Portfolio"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast"/><meta name="twitter:description" content="Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences."/><meta name="twitter:image" content="http://localhost:3000/og-image.jpg"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div hidden=""><!--$--><!--/$--></div><div style="font-family:system-ui,&quot;Segoe UI&quot;,Roboto,Helvetica,Arial,sans-serif,&quot;Apple Color Emoji&quot;,&quot;Segoe UI Emoji&quot;;height:100vh;text-align:center;display:flex;flex-direction:column;align-items:center;justify-content:center"><div><style>body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}</style><h1 class="next-error-h1" style="display:inline-block;margin:0 20px 0 0;padding:0 23px 0 0;font-size:24px;font-weight:500;vertical-align:top;line-height:49px">404</h1><div style="display:inline-block"><h2 style="font-size:14px;font-weight:400;line-height:49px;margin:0">This page could not be found.</h2></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-bfa3c263a159a400.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1483,[\"177\",\"static/chunks/app/layout-f41564b1781e53a9.js\"],\"ThemeProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[9665,[],\"OutletBoundary\"]\n7:I[4911,[],\"AsyncMetadataOutlet\"]\n9:I[9665,[],\"ViewportBoundary\"]\nb:I[9665,[],\"MetadataBoundary\"]\nc:\"$Sreact.suspense\"\ne:I[8393,[],\"\"]\n:HL[\"/_next/static/css/641cf9563d92ca3b.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"l4Q0NMTmhHtPBXa2ZyD85\",\"p\":\"\",\"c\":[\"\",\"_not-found\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/641cf9563d92ca3b.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"/_not-found\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",[\"$\",\"$L7\",null,{\"promise\":\"$@8\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[[\"$\",\"meta\",null,{\"name\":\"robots\",\"content\":\"noindex\"}],[[\"$\",\"$L9\",null,{\"children\":\"$La\"}],null],[\"$\",\"$Lb\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$c\",null,{\"fallback\":null,\"children\":\"$Ld\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"a:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"8:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Abhishek Kumar Singh\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"Abhishek Kumar Singh,Frontend Developer,Full Stack Developer,React Developer,Next.js,IIT Roorkee,College of Engineering Roorkee,Computer Science,Web Developer,JavaScript,TypeScript,Tailwind CSS,Portfolio\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Abhishek Kumar Singh\"}],[\"$\",\"meta\",\"5\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"6\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"7\",{\"name\":\"resume-url\",\"content\":\"/resume.pdf\"}],[\"$\",\"meta\",\"8\",{\"name\":\"resume-download\",\"content\":\"/api/resume\"}],[\"$\",\"meta\",\"9\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://abhishek-portfolio.vercel.app\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"Abhishek Kumar Singh Portfolio\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:alt\",\"content\":\"Abhishek Kumar Singh - Portfolio\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:title\",\"content\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:description\",\"content\":\"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences.\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"d:\"$8:metadata\"\n"])</script></body></html>