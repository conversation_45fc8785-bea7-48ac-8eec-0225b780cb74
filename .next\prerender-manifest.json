{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "03a41c96c61b2a47c7a5904f76124f85", "previewModeSigningKey": "8d57c2fc2420a9f37b3be36feb6226d53edbba2fa7b4069f930834edc4b197dc", "previewModeEncryptionKey": "73b6e3063fb782a3a781a1857f52f11df24704dfdaec38beba0f5e476a3b3024"}}