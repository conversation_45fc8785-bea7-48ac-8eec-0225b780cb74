(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c});var s=t(5155),r=t(2115),i=t(4624),l=t(2085),n=t(9434);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",neo:"neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold transition-all duration-200"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,a)=>{let{className:t,variant:r,size:l,asChild:c=!1,...d}=e,m=c?i.DX:"button";return(0,s.jsx)(m,{className:(0,n.cn)(o({variant:r,size:l,className:t})),ref:a,...d})});c.displayName="Button"},433:(e,a,t)=>{"use strict";t.d(a,{Contact:()=>g});var s=t(5155),r=t(2984),i=t(8883),l=t(9099),n=t(2894),o=t(4516),c=t(1788),d=t(1366),m=t(2486),x=t(6695),h=t(285);let p=[{icon:i.A,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>",color:"from-blue-500 to-cyan-500"},{icon:l.A,label:"GitHub",value:"Abhishek-kumarsingh",href:"https://github.com/Abhishek-kumarsingh",color:"from-gray-700 to-gray-900"},{icon:n.A,label:"LinkedIn",value:"abhishek-kumar-singh",href:"https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/",color:"from-blue-600 to-blue-800"},{icon:o.A,label:"Location",value:"Roorkee, India",href:"#",color:"from-green-500 to-emerald-600"}];function g(){return(0,s.jsx)("section",{id:"contact",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Let's Connect"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Ready to collaborate on exciting projects or discuss opportunities? I'd love to hear from you!"})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-start",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"Get in Touch"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed mb-8",children:"I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology and development. Whether you're looking for a frontend developer, need help with a project, or want to collaborate on something exciting, feel free to reach out!"})]}),(0,s.jsx)("div",{className:"space-y-4",children:p.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1*a},viewport:{once:!0},children:(0,s.jsx)(x.Zp,{className:"glass dark:glass-dark border-0 hover:scale-105 transition-all duration-300 group cursor-pointer",children:(0,s.jsx)(x.Wu,{className:"p-4",children:(0,s.jsxs)("a",{href:e.href,target:e.href.startsWith("http")?"_blank":"_self",rel:e.href.startsWith("http")?"noopener noreferrer":"",className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(e.color," group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)(e.icon,{className:"h-5 w-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.label}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e.value})]})]})})})},a))}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"space-y-4",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Quick Actions"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsxs)(h.$,{variant:"neo",className:"flex-1",onClick:()=>window.open("mailto:<EMAIL>","_blank"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Send Email"]}),(0,s.jsxs)(h.$,{variant:"outline",className:"flex-1 btn-glass-premium hover-lift-premium",onClick:()=>{window.open("/resume.pdf","_blank");let e=document.createElement("a");e.href="/api/resume",e.download="Abhishek_Kumar_Singh_Resume.pdf",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Download Resume"]})]})]})]}),(0,s.jsx)(r.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},children:(0,s.jsxs)(x.Zp,{className:"glass dark:glass-dark border-0",children:[(0,s.jsx)(x.aR,{children:(0,s.jsxs)(x.ZB,{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,s.jsx)(d.A,{className:"mr-3 h-6 w-6"}),"Send a Message"]})}),(0,s.jsxs)(x.Wu,{className:"space-y-6",children:[(0,s.jsxs)("form",{className:"space-y-6",onSubmit:e=>e.preventDefault(),children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"First Name"}),(0,s.jsx)("input",{type:"text",className:"w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"John"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Last Name"}),(0,s.jsx)("input",{type:"text",className:"w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Doe"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",className:"w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"<EMAIL>"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Subject"}),(0,s.jsx)("input",{type:"text",className:"w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Project Collaboration"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Message"}),(0,s.jsx)("textarea",{rows:5,className:"w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none",placeholder:"Tell me about your project or opportunity..."})]}),(0,s.jsxs)(h.$,{type:"submit",variant:"neo",className:"w-full",onClick:()=>{alert("Thank you for your message! I'll get back to you soon.")},children:[(0,s.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Send Message"]})]}),(0,s.jsx)("div",{className:"text-center pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Prefer email? Reach me directly at"," ",(0,s.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 dark:text-blue-400 hover:underline font-medium",children:"<EMAIL>"})]})})]})]})})]}),(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"mt-16 text-center",children:(0,s.jsx)(x.Zp,{className:"glass dark:glass-dark border-0 max-w-2xl mx-auto",children:(0,s.jsxs)(x.Wu,{className:"p-8",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Ready to Build Something Amazing?"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6 leading-relaxed",children:"I'm currently open to new opportunities and exciting projects. Let's discuss how we can work together to create something extraordinary!"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,s.jsxs)(h.$,{variant:"neo",size:"lg",onClick:()=>window.open("mailto:<EMAIL>?subject=Let's Collaborate!","_blank"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-5 w-5"}),"Start a Conversation"]}),(0,s.jsxs)(h.$,{variant:"outline",size:"lg",className:"glass dark:glass-dark",onClick:()=>window.open("https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/","_blank"),children:[(0,s.jsx)(n.A,{className:"mr-2 h-5 w-5"}),"Connect on LinkedIn"]})]})]})})})]})})}},1440:(e,a,t)=>{"use strict";t.d(a,{Navigation:()=>g});var s=t(5155),r=t(2115),i=t(2984),l=t(1788),n=t(4416),o=t(4783),c=t(2098),d=t(3509),m=t(4455),x=t(285);function h(){let{theme:e,setTheme:a}=(0,m.D)(),[t,i]=r.useState(!1);return(r.useEffect(()=>{i(!0)},[]),t)?(0,s.jsxs)(x.$,{variant:"ghost",size:"icon",onClick:()=>a("light"===e?"dark":"light"),className:"glass dark:glass-dark hover:scale-110 transition-all duration-300",children:[(0,s.jsx)(c.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,s.jsx)(d.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]}):null}let p=[{name:"Home",href:"#home"},{name:"About",href:"#about"},{name:"Experience",href:"#experience"},{name:"Projects",href:"#projects"},{name:"Skills",href:"#skills"},{name:"Leadership",href:"#leadership"},{name:"Contact",href:"#contact"}];function g(){let[e,a]=(0,r.useState)(!1),[t,c]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{c(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,s.jsxs)(i.P.nav,{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-500 ".concat(t?"glass-ultra backdrop-blur-xl border-b border-white/10":"bg-transparent"),initial:{y:-100},animate:{y:0},transition:{duration:.8,type:"spring",stiffness:100},children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,s.jsx)(i.P.div,{className:"flex-shrink-0",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)("a",{href:"#home",className:"text-2xl font-bold gradient-text-premium text-shadow-premium hover:text-glow transition-all duration-300",children:"AKS"})}),(0,s.jsx)("div",{className:"hidden md:block",children:(0,s.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:p.map(e=>(0,s.jsx)(i.P.a,{href:e.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover",whileHover:{scale:1.05,y:-2},whileTap:{scale:.95},children:e.name},e.name))})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(i.P.a,{href:"/resume.pdf",target:"_blank",rel:"noopener noreferrer",className:"hidden md:flex items-center space-x-2 px-4 py-2 btn-glass-premium text-sm font-medium hover-lift-premium magnetic-hover",whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Resume"})]}),(0,s.jsx)(h,{}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(x.$,{variant:"ghost",size:"icon",onClick:()=>a(!e),className:"glass dark:glass-dark",children:e?(0,s.jsx)(n.A,{className:"h-6 w-6"}):(0,s.jsx)(o.A,{className:"h-6 w-6"})})})]})]})}),(0,s.jsx)(i.P.div,{className:"md:hidden ".concat(e?"block":"hidden"),initial:{opacity:0,height:0},animate:{opacity:+!!e,height:e?"auto":0},transition:{duration:.3},children:(0,s.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 glass-card dark:glass-card-dark backdrop-blur-xl",children:[p.map(e=>(0,s.jsx)(i.P.a,{href:e.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200",onClick:()=>a(!1),whileHover:{x:10},children:e.name},e.name)),(0,s.jsxs)(i.P.a,{href:"/resume.pdf",target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200",onClick:()=>a(!1),whileHover:{x:10},children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Download Resume"})]})]})})]})}},1634:(e,a,t)=>{"use strict";t.d(a,{Certifications:()=>x});var s=t(5155),r=t(2984),i=t(9074),l=t(2915),n=t(9037),o=t(3786),c=t(6695),d=t(285);let m=[{title:"React Development",issuer:"Board Infinity",date:"2024",description:"Comprehensive certification covering React fundamentals, hooks, state management, and modern React patterns.",skills:["React.js","JSX","Hooks","State Management","Component Architecture"],credentialUrl:"#",color:"from-blue-500 to-cyan-500",logo:"⚛️"},{title:"HTML, CSS & JavaScript",issuer:"Coursera",date:"2023",description:"Complete web development fundamentals covering HTML5, CSS3, and modern JavaScript ES6+ features.",skills:["HTML5","CSS3","JavaScript","DOM Manipulation","Responsive Design"],credentialUrl:"#",color:"from-orange-500 to-red-500",logo:"\uD83C\uDF10"},{title:"Cybersecurity Fundamentals",issuer:"IBM",date:"2023",description:"Comprehensive cybersecurity training covering threat analysis, security protocols, and best practices.",skills:["Security Protocols","Threat Analysis","Risk Assessment","Network Security"],credentialUrl:"#",color:"from-red-500 to-pink-500",logo:"\uD83D\uDD12"},{title:"Java Programming",issuer:"GeeksforGeeks",date:"2023",description:"Advanced Java programming certification covering OOP concepts, data structures, and algorithms.",skills:["Java","OOP","Data Structures","Algorithms","Problem Solving"],credentialUrl:"#",color:"from-green-500 to-teal-500",logo:"☕"},{title:"Full Stack Development Bootcamp",issuer:"GeeksforGeeks",date:"2024",description:"Intensive full-stack development program covering both frontend and backend technologies.",skills:["Full Stack","MERN Stack","Database Design","API Development","Deployment"],credentialUrl:"#",color:"from-purple-500 to-indigo-500",logo:"\uD83D\uDE80"}];function x(){return(0,s.jsx)("section",{id:"certifications",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Certifications"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Continuous learning and professional development achievements"})]}),(0,s.jsx)("div",{className:"grid lg:grid-cols-2 gap-8",children:m.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*a},viewport:{once:!0},children:(0,s.jsxs)(c.Zp,{className:"glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group",children:[(0,s.jsx)(c.aR,{children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(e.color," text-2xl"),children:e.logo}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.ZB,{className:"text-xl font-bold text-gray-900 dark:text-white mb-1",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 font-medium",children:e.issuer})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500 dark:text-gray-400",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:e.date})]})]})}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e.description}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-3 flex items-center",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2 text-green-600"}),"Skills Covered:"]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.skills.map((e,a)=>(0,s.jsx)(r.P.span,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.3,delay:.05*a},viewport:{once:!0},className:"px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200",children:e},a))})]}),(0,s.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)(d.$,{variant:"outline",size:"sm",className:"w-full group-hover:scale-105 transition-transform duration-200",onClick:()=>window.open(e.credentialUrl,"_blank"),children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"View Credential",(0,s.jsx)(o.A,{className:"ml-2 h-4 w-4"})]})})]})]})},a))}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 text-yellow-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"5+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Professional Certifications"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(l.A,{className:"h-8 w-8 text-green-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"100%"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Completion Rate"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(i.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"2024"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Latest Certification"})]})]})]})})}},2191:(e,a,t)=>{Promise.resolve().then(t.bind(t,2836)),Promise.resolve().then(t.bind(t,1440)),Promise.resolve().then(t.bind(t,4334)),Promise.resolve().then(t.bind(t,1634)),Promise.resolve().then(t.bind(t,433)),Promise.resolve().then(t.bind(t,9067)),Promise.resolve().then(t.bind(t,3430)),Promise.resolve().then(t.bind(t,7711)),Promise.resolve().then(t.bind(t,9530)),Promise.resolve().then(t.bind(t,8957)),Promise.resolve().then(t.bind(t,8397))},2836:(e,a,t)=>{"use strict";t.d(a,{FloatingShapes:()=>o});var s=t(5155),r=t(2984),i=t(2115);function l(e){let{children:a,fallback:t=null}=e,[r,l]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{l(!0)},[]),r)?(0,s.jsx)(s.Fragment,{children:a}):(0,s.jsx)(s.Fragment,{children:t})}function n(){let[e,a]=(0,i.useState)([]);return(0,i.useEffect)(()=>{let e=[],t=["from-blue-400 to-purple-500","from-pink-400 to-red-500","from-green-400 to-blue-500","from-yellow-400 to-orange-500","from-purple-400 to-pink-500","from-cyan-400 to-blue-500","from-indigo-400 to-purple-500","from-emerald-400 to-teal-500"],s=["circle","triangle","square","diamond","hexagon"];for(let a=0;a<12;a++)e.push({id:a,x:100*Math.random(),y:100*Math.random(),size:60*Math.random()+20,color:t[Math.floor(Math.random()*t.length)],duration:20*Math.random()+15,delay:5*Math.random(),shape:s[Math.floor(Math.random()*s.length)]});a(e)},[]),(0,s.jsxs)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none z-0",children:[e.map(e=>(0,s.jsx)(r.P.div,{className:"absolute bg-gradient-to-r ".concat(e.color," opacity-15 blur-[0.5px]"),style:{left:"".concat(e.x,"%"),top:"".concat(e.y,"%"),width:"".concat(e.size,"px"),height:"".concat(e.size,"px"),clipPath:(e=>{switch(e){case"triangle":return"polygon(50% 0%, 0% 100%, 100% 100%)";case"square":return"polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)";case"diamond":return"polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)";case"hexagon":return"polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%)";default:return"none"}})(e.shape),borderRadius:"circle"===e.shape?"50%":"8px"},animate:{y:[0,-40,20,-30,0],x:[0,30,-20,25,0],rotate:[0,180,270,360],scale:[1,1.2,.8,1.1,1],opacity:[.15,.25,.1,.2,.15]},transition:{duration:e.duration,repeat:1/0,ease:"easeInOut",delay:e.delay}},e.id)),(0,s.jsx)(r.P.div,{className:"absolute top-1/4 left-1/4 w-32 h-32 holographic rounded-full opacity-20 blur-sm",animate:{y:[0,-50,0],x:[0,40,0],scale:[1,1.3,1]},transition:{duration:25,repeat:1/0,ease:"easeInOut"}}),(0,s.jsx)(r.P.div,{className:"absolute top-3/4 right-1/4 w-24 h-24 holographic rounded-full opacity-25 blur-sm",animate:{y:[0,30,0],x:[0,-30,0],scale:[1,.8,1]},transition:{duration:20,repeat:1/0,ease:"easeInOut",delay:2}}),(0,s.jsx)(r.P.div,{className:"absolute top-1/2 right-1/3 w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 opacity-20 morphing",animate:{y:[0,-25,0],x:[0,25,0],rotate:[0,90,180,270,360]},transition:{duration:30,repeat:1/0,ease:"linear"}}),(0,s.jsx)(r.P.div,{className:"absolute top-1/3 right-1/5 w-16 h-16 glass-ultra rounded-lg opacity-30",animate:{y:[0,-20,0],x:[0,15,0],rotateY:[0,180,360]},transition:{duration:18,repeat:1/0,ease:"easeInOut",delay:1}}),(0,s.jsx)(r.P.div,{className:"absolute bottom-1/3 left-1/5 w-12 h-12 glass-ultra rounded-full opacity-25",animate:{y:[0,25,0],x:[0,-20,0],scale:[1,1.4,1]},transition:{duration:22,repeat:1/0,ease:"easeInOut",delay:3}})]})}function o(){return(0,s.jsx)(l,{fallback:(0,s.jsx)("div",{className:"fixed inset-0 overflow-hidden pointer-events-none z-0"}),children:(0,s.jsx)(n,{})})}},3430:(e,a,t)=>{"use strict";t.d(a,{Footer:()=>m});var s=t(5155),r=t(2984),i=t(9881),l=t(9099),n=t(2894),o=t(8883),c=t(1976),d=t(285);function m(){return(0,s.jsx)("footer",{className:"relative py-12 border-t border-gray-200 dark:border-gray-800",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"flex justify-center mb-8",children:(0,s.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"glass dark:glass-dark hover:scale-110 transition-all duration-300",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"text-center md:text-left",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold gradient-text mb-4",children:"Abhishek Kumar Singh"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:"Frontend Developer & Full Stack Enthusiast passionate about creating exceptional digital experiences with modern web technologies."})]}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"text-center",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Quick Links"}),(0,s.jsx)("div",{className:"space-y-2",children:[{name:"About",href:"#about"},{name:"Projects",href:"#projects"},{name:"Skills",href:"#skills"},{name:"Experience",href:"#experience"},{name:"Contact",href:"#contact"}].map(e=>(0,s.jsx)("div",{children:(0,s.jsx)("a",{href:e.href,className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200",children:e.name})},e.name))})]}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},className:"text-center md:text-right",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Connect With Me"}),(0,s.jsxs)("div",{className:"flex justify-center md:justify-end space-x-4 mb-4",children:[(0,s.jsx)(r.P.a,{href:"https://github.com/Abhishek-kumarsingh",target:"_blank",rel:"noopener noreferrer",className:"p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:(0,s.jsx)(l.A,{className:"h-5 w-5"})}),(0,s.jsx)(r.P.a,{href:"https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/",target:"_blank",rel:"noopener noreferrer",className:"p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:(0,s.jsx)(n.A,{className:"h-5 w-5"})}),(0,s.jsx)(r.P.a,{href:"mailto:<EMAIL>",className:"p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300",whileHover:{scale:1.1},whileTap:{scale:.95},children:(0,s.jsx)(o.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"<EMAIL>"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Roorkee, India"})]})]}),(0,s.jsx)(r.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.4},viewport:{once:!0},className:"pt-8 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,s.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 text-sm flex items-center",children:["\xa9 2024 Abhishek Kumar Singh. Made with"," ",(0,s.jsx)(c.A,{className:"h-4 w-4 text-red-500 mx-1"}),"using Next.js & Tailwind CSS"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("span",{children:"Final Year B.Tech CS Student"}),(0,s.jsx)("span",{children:"•"}),(0,s.jsx)("span",{children:"College of Engineering Roorkee"})]})]})})]})})}},4334:(e,a,t)=>{"use strict";t.d(a,{About:()=>c});var s=t(5155),r=t(2984),i=t(7949),l=t(9074),n=t(4516),o=t(6695);function c(){return(0,s.jsx)("section",{id:"about",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"About Me"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Passionate about crafting digital experiences that make a difference"})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-6",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"w-80 h-80 mx-auto rounded-2xl overflow-hidden neo-brutal bg-gradient-to-br from-blue-400 to-purple-600 p-1",children:(0,s.jsx)("img",{src:"https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400",alt:"Abhishek Kumar Singh",className:"w-full h-full object-cover rounded-xl"})})}),(0,s.jsx)(o.Zp,{className:"glass dark:glass-dark border-0",children:(0,s.jsxs)(o.Wu,{className:"p-6 space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(i.A,{className:"h-5 w-5 text-blue-600"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"B.Tech Computer Science, College of Engineering Roorkee"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(l.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Graduating 2025"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(n.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"Roorkee, India"})]})]})})]}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"prose prose-lg dark:prose-invert max-w-none",children:[(0,s.jsxs)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:["I'm a passionate final-year Computer Science student with a strong focus on",(0,s.jsx)("span",{className:"font-semibold text-blue-600 dark:text-blue-400",children:" frontend development"})," and full-stack capabilities. My journey in tech has been driven by curiosity and a desire to create meaningful digital experiences."]}),(0,s.jsxs)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:["During my internship at ",(0,s.jsx)("span",{className:"font-semibold text-purple-600 dark:text-purple-400",children:"IIT Roorkee"}),", I had the opportunity to work on high-impact projects, revamping government portals and integrating complex systems. This experience taught me the importance of scalable, maintainable code and user-centered design."]}),(0,s.jsxs)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:["I'm particularly passionate about ",(0,s.jsx)("span",{className:"font-semibold text-green-600 dark:text-green-400",children:"React.js"}),",",(0,s.jsx)("span",{className:"font-semibold text-blue-600 dark:text-blue-400",children:" Next.js"}),", and modern web technologies. I enjoy solving complex problems, learning new technologies, and contributing to projects that make a positive impact on users' lives."]}),(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"When I'm not coding, you'll find me organizing tech events, mentoring fellow students, or exploring the latest trends in web development and AI. I believe in continuous learning and sharing knowledge with the community."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 mt-8",children:[(0,s.jsxs)(r.P.div,{className:"text-center p-4 glass dark:glass-dark rounded-xl",whileHover:{scale:1.05},children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:"30+"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"APIs Integrated"})]}),(0,s.jsxs)(r.P.div,{className:"text-center p-4 glass dark:glass-dark rounded-xl",whileHover:{scale:1.05},children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:"45%"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Bug Reduction"})]}),(0,s.jsxs)(r.P.div,{className:"text-center p-4 glass dark:glass-dark rounded-xl",whileHover:{scale:1.05},children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"70+"}),(0,s.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Team Members Led"})]})]})]})]})]})})}},6695:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n});var s=t(5155),r=t(2115),i=t(9434);let l=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",t),...r})});l.displayName="Card";let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});n.displayName="CardHeader";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("font-semibold leading-none tracking-tight",t),...r})});o.displayName="CardTitle",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})}).displayName="CardDescription";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",t),...r})});c.displayName="CardContent",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},7711:(e,a,t)=>{"use strict";t.d(a,{Hero:()=>h});var s=t(5155),r=t(2115),i=t(2984),l=t(1788),n=t(9099),o=t(2894),c=t(8883),d=t(8832),m=t(285);let x=["Frontend Developer","Full Stack Developer","React Specialist","UI/UX Enthusiast","Problem Solver"];function h(){let[e,a]=(0,r.useState)(0),[t,h]=(0,r.useState)(""),[p,g]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let s=x[e],r=setTimeout(()=>{p?t.length>0?h(t.slice(0,-1)):(g(!1),a(e=>(e+1)%x.length)):t.length<s.length?h(s.slice(0,t.length+1)):setTimeout(()=>g(!0),2e3)},p?50:100);return()=>clearTimeout(r)},[t,p,e]),(0,s.jsxs)("section",{id:"home",className:"min-h-screen flex items-center justify-center relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 gradient-bg dark:gradient-bg-dark"}),(0,s.jsxs)("div",{className:"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,s.jsxs)(i.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},children:[(0,s.jsx)(i.P.h1,{className:"text-5xl md:text-7xl font-bold mb-6 text-premium",initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.8,delay:.2},children:(0,s.jsx)("span",{className:"gradient-text-premium text-shadow-premium",children:"Abhishek Kumar Singh"})}),(0,s.jsx)(i.P.div,{className:"text-2xl md:text-3xl font-semibold mb-8 h-12 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8,delay:.4},children:(0,s.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[t,(0,s.jsx)("span",{className:"typing-cursor text-blue-600"})]})}),(0,s.jsx)(i.P.p,{className:"text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},children:"Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about creating exceptional digital experiences with modern web technologies. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and full-stack development."}),(0,s.jsxs)(i.P.div,{className:"flex flex-col sm:flex-row items-center justify-center gap-6 mb-12",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},children:[(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsx)(m.$,{variant:"neo",size:"lg",className:"btn-neo-premium text-lg hover-lift-premium magnetic-hover",onClick:()=>{var e;return null==(e=document.getElementById("projects"))?void 0:e.scrollIntoView({behavior:"smooth"})},children:"View My Work"})}),(0,s.jsx)(i.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,s.jsxs)(m.$,{variant:"outline",size:"lg",className:"btn-glass-premium text-lg hover-lift-premium magnetic-hover",onClick:()=>{window.open("/resume.pdf","_blank");let e=document.createElement("a");e.href="/api/resume",e.download="Abhishek_Kumar_Singh_Resume.pdf",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,s.jsx)(l.A,{className:"mr-2 h-5 w-5"}),"Download Resume"]})})]}),(0,s.jsxs)(i.P.div,{className:"flex items-center justify-center space-x-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},children:[(0,s.jsx)(i.P.a,{href:"https://github.com/Abhishek-kumarsingh",target:"_blank",rel:"noopener noreferrer",className:"p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium",whileHover:{scale:1.15,rotateY:180},whileTap:{scale:.9},transition:{type:"spring",stiffness:400,damping:17},children:(0,s.jsx)(n.A,{className:"h-6 w-6"})}),(0,s.jsx)(i.P.a,{href:"https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/",target:"_blank",rel:"noopener noreferrer",className:"p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium",whileHover:{scale:1.15,rotateY:180},whileTap:{scale:.9},transition:{type:"spring",stiffness:400,damping:17},children:(0,s.jsx)(o.A,{className:"h-6 w-6"})}),(0,s.jsx)(i.P.a,{href:"mailto:<EMAIL>",className:"p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium",whileHover:{scale:1.15,rotateY:180},whileTap:{scale:.9},transition:{type:"spring",stiffness:400,damping:17},children:(0,s.jsx)(c.A,{className:"h-6 w-6"})})]})]}),(0,s.jsx)(i.P.div,{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:1.2},children:(0,s.jsx)(i.P.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"cursor-pointer",onClick:()=>{var e;return null==(e=document.getElementById("about"))?void 0:e.scrollIntoView({behavior:"smooth"})},children:(0,s.jsx)(d.A,{className:"h-6 w-6 text-gray-600 dark:text-gray-400"})})})]})]})}},8397:(e,a,t)=>{"use strict";t.d(a,{Skills:()=>g});var s=t(5155),r=t(2984),i=t(4869),l=t(5487),n=t(4213),o=t(2775),c=t(9621),d=t(1539),m=t(3127),x=t(6695);let h=[{title:"Frontend Development",icon:i.A,color:"from-blue-500 to-cyan-500",skills:[{name:"React",level:95},{name:"Next.js",level:90},{name:"TypeScript",level:85},{name:"JavaScript",level:95},{name:"Tailwind CSS",level:90},{name:"HTML/CSS",level:95}]},{title:"Backend Development",icon:l.A,color:"from-green-500 to-emerald-500",skills:[{name:"Node.js",level:85},{name:"Spring Boot",level:80},{name:"FastAPI",level:75},{name:"Express.js",level:85},{name:"REST APIs",level:90},{name:"GraphQL",level:70}]},{title:"Database & Storage",icon:n.A,color:"from-purple-500 to-pink-500",skills:[{name:"MongoDB",level:85},{name:"MySQL",level:80},{name:"PostgreSQL",level:75},{name:"Redis",level:70},{name:"Firebase",level:80},{name:"Supabase",level:75}]},{title:"DevOps & Tools",icon:o.A,color:"from-orange-500 to-red-500",skills:[{name:"Docker",level:80},{name:"Git/GitHub",level:95},{name:"Vercel",level:90},{name:"AWS",level:70},{name:"RabbitMQ",level:75},{name:"JWT",level:85}]}],p=[{name:"React",icon:"⚛️"},{name:"Next.js",icon:"▲"},{name:"TypeScript",icon:"\uD83D\uDCD8"},{name:"JavaScript",icon:"\uD83D\uDFE8"},{name:"Tailwind",icon:"\uD83C\uDFA8"},{name:"Node.js",icon:"\uD83D\uDFE2"},{name:"Spring Boot",icon:"\uD83C\uDF43"},{name:"MongoDB",icon:"\uD83C\uDF43"},{name:"MySQL",icon:"\uD83D\uDC2C"},{name:"Docker",icon:"\uD83D\uDC33"},{name:"Git",icon:"\uD83D\uDCDA"},{name:"AWS",icon:"☁️"}];function g(){return(0,s.jsx)("section",{id:"skills",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Technical Skills"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Comprehensive expertise across the full development stack"})]}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"mb-16",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white",children:"Technology Stack"}),(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:p.map((e,a)=>(0,s.jsxs)(r.P.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.5,delay:.05*a},viewport:{once:!0},whileHover:{scale:1.1,y:-5},className:"flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium",children:[(0,s.jsx)("span",{className:"text-2xl",children:e.icon}),(0,s.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:e.name})]},a))})]}),(0,s.jsx)("div",{className:"grid lg:grid-cols-2 gap-8",children:h.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*a},viewport:{once:!0},children:(0,s.jsxs)(x.Zp,{className:"card-premium border-0 h-full hover-lift-premium hover-tilt",children:[(0,s.jsx)(x.aR,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(e.color),children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,s.jsx)(x.ZB,{className:"text-xl font-bold text-gray-900 dark:text-white",children:e.title})]})}),(0,s.jsx)(x.Wu,{className:"space-y-4",children:e.skills.map((a,t)=>(0,s.jsxs)(r.P.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:a.name}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[a.level,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,s.jsx)(r.P.div,{className:"h-2 rounded-full bg-gradient-to-r ".concat(e.color),initial:{width:0},whileInView:{width:"".concat(a.level,"%")},transition:{duration:1,delay:.1*t},viewport:{once:!0}})})]},t))})]})},a))}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsx)(x.Zp,{className:"glass-card dark:glass-card-dark border-0 text-center hover-lift",children:(0,s.jsxs)(x.Wu,{className:"p-6",children:[(0,s.jsx)(c.A,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:"Clean Code"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Writing maintainable, scalable, and well-documented code following best practices"})]})}),(0,s.jsx)(x.Zp,{className:"glass-card dark:glass-card-dark border-0 text-center hover-lift",children:(0,s.jsxs)(x.Wu,{className:"p-6",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 text-yellow-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:"Performance"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Optimizing applications for speed, efficiency, and excellent user experience"})]})}),(0,s.jsx)(x.Zp,{className:"glass-card dark:glass-card-dark border-0 text-center hover-lift",children:(0,s.jsxs)(x.Wu,{className:"p-6",children:[(0,s.jsx)(m.A,{className:"h-12 w-12 text-purple-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:"UI/UX Design"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Creating intuitive and visually appealing user interfaces with modern design principles"})]})})]})]})})}},8957:(e,a,t)=>{"use strict";t.d(a,{Projects:()=>h});var s=t(5155),r=t(2984),i=t(3786),l=t(9099),n=t(9074),o=t(3109),c=t(7580),d=t(6695),m=t(285);let x=[{title:"Threat Zone Prediction System",description:"A comprehensive real-time disaster alert platform that leverages machine learning models to predict and monitor potential threat zones with 88% accuracy.",longDescription:"Built a sophisticated disaster management system using React for the frontend, Node.js for backend services, and MongoDB for data storage. Integrated multiple ML models for threat prediction and real-time monitoring capabilities.",technologies:["React","Node.js","MongoDB","Machine Learning","Python","Express.js"],features:["Real-time threat monitoring and alerts","88% accuracy in disaster prediction","Interactive dashboard with data visualization","Multi-source data integration","Mobile-responsive design"],liveUrl:"https://threat-sigma.vercel.app",githubUrl:"https://github.com/Abhishek-kumarsingh/Threat-Zone-Prediction",status:"Completed",color:"from-red-500 to-orange-600",image:"https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&cs=tinysrgb&w=600"},{title:"AI Interview Platform",description:"An intelligent interview platform powered by AI that provides real-time feedback, code evaluation, and comprehensive analytics for both candidates and recruiters.",longDescription:"Developed using Next.js with Gemini API integration for AI-powered interviews. Features Monaco Editor for code challenges, JWT OAuth for secure authentication, and detailed analytics dashboards.",technologies:["Next.js","Gemini API","Monaco Editor","JWT","OAuth","TypeScript"],features:["AI-powered interview questions","Real-time code evaluation","Comprehensive analytics dashboard","Secure authentication system","Multi-language code support"],liveUrl:"https://aithors.vercel.app",githubUrl:"https://github.com/Abhishek-kumarsingh/Aithors",status:"Completed",color:"from-blue-500 to-purple-600",image:"https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=600"},{title:"Learning Management System",description:"A comprehensive LMS platform with payment integration, real-time communication, and advanced course management features for educators and students.",longDescription:"Currently developing a full-featured LMS using Next.js for the frontend, Spring Boot for backend services, Stripe for payment processing, and RabbitMQ for real-time messaging and notifications.",technologies:["Next.js","Spring Boot","Stripe","RabbitMQ","PostgreSQL","Redis"],features:["Course creation and management","Integrated payment system","Real-time messaging and notifications","Progress tracking and analytics","Multi-role user management"],liveUrl:"https://lms-neon-tau.vercel.app",githubUrl:"https://github.com/Abhishek-kumarsingh/LMS-Platform",status:"In Progress",color:"from-green-500 to-teal-600",image:"https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=600"},{title:"Threat Monitoring System",description:"Advanced threat monitoring system with real-time data processing, machine learning predictions, and comprehensive visualization dashboards.",longDescription:"Building a sophisticated monitoring system using FastAPI for high-performance backend, Spring Boot for microservices, Docker for containerization, and XGBoost for predictive analytics.",technologies:["FastAPI","Spring Boot","Docker","Recharts","WebSockets","XGBoost"],features:["Real-time threat detection","Machine learning predictions","Interactive data visualization","Microservices architecture","Scalable containerized deployment"],liveUrl:"https://threat-monitoring.vercel.app",githubUrl:"https://github.com/Abhishek-kumarsingh/Threat-Monitoring-System",status:"In Progress",color:"from-purple-500 to-pink-600",image:"https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&cs=tinysrgb&w=600"}];function h(){return(0,s.jsx)("section",{id:"projects",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Featured Projects"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Showcasing innovative solutions and technical expertise"})]}),(0,s.jsx)("div",{className:"grid lg:grid-cols-2 gap-8",children:x.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*a},viewport:{once:!0},children:(0,s.jsxs)(d.Zp,{className:"card-premium border-0 h-full group hover-lift-premium hover-tilt",children:[(0,s.jsxs)(d.aR,{className:"pb-4",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden rounded-lg mb-4",children:[(0,s.jsx)("img",{src:e.image,alt:e.title,className:"w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"}),(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ".concat(e.color," text-white"),children:e.status})})]}),(0,s.jsx)(d.ZB,{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed",children:e.description})]}),(0,s.jsxs)(d.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-3",children:"Key Features:"}),(0,s.jsx)("ul",{className:"space-y-2",children:e.features.map((e,a)=>(0,s.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},a))})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.technologies.map((e,a)=>(0,s.jsx)("span",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium",children:e},a))}),(0,s.jsxs)("div",{className:"flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700",children:[(0,s.jsxs)(m.$,{variant:"outline",size:"sm",className:"flex-1",onClick:()=>window.open(e.liveUrl,"_blank"),children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Live Demo"]}),(0,s.jsxs)(m.$,{variant:"outline",size:"sm",className:"flex-1",onClick:()=>window.open(e.githubUrl,"_blank"),children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Source Code"]})]})]})]})},a))}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 text-blue-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"4+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Projects Completed"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 text-green-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"88%"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"ML Model Accuracy"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)(c.A,{className:"h-8 w-8 text-purple-600 mx-auto mb-4"}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"1000+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:"Users Impacted"})]})]})]})})}},9067:(e,a,t)=>{"use strict";t.d(a,{Experience:()=>d});var s=t(5155),r=t(2984),i=t(8136),l=t(4516),n=t(9074),o=t(6695);let c=[{title:"Frontend Developer Intern",company:"IIT Roorkee",location:"Roorkee, India",duration:"Jun 2024 - Aug 2024",type:"Internship",description:["Revamped a high-traffic government portal using React.js, Tailwind CSS, and Redux Toolkit","Integrated 30+ RESTful APIs to enhance functionality and user experience","Reduced system bugs by 45% through comprehensive testing and code optimization","Collaborated with cross-functional teams to deliver scalable solutions","Implemented responsive design principles for optimal mobile and desktop experience"],technologies:["React.js","Tailwind CSS","Redux Toolkit","REST APIs","JavaScript"],color:"from-blue-500 to-purple-600"}];function d(){return(0,s.jsx)("section",{id:"experience",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Experience"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Professional journey and key contributions"})]}),(0,s.jsx)("div",{className:"space-y-8",children:c.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*a},viewport:{once:!0},children:(0,s.jsxs)(o.Zp,{className:"glass dark:glass-dark border-0 hover:scale-[1.02] transition-all duration-300",children:[(0,s.jsx)(o.aR,{children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.ZB,{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-gray-600 dark:text-gray-400",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:e.company})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.location})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col items-end space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.duration})]}),(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ".concat(e.color," text-white"),children:e.type})]})]})}),(0,s.jsxs)(o.Wu,{className:"space-y-6",children:[(0,s.jsx)("ul",{className:"space-y-3",children:e.description.map((e,a)=>(0,s.jsxs)(r.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*a},viewport:{once:!0},className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e})]},a))}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700",children:e.technologies.map((e,a)=>(0,s.jsx)(r.P.span,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.3,delay:.05*a},viewport:{once:!0},className:"px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200",children:e},a))})]})]})},a))}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"mt-16",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-center mb-8 gradient-text",children:"Education"}),(0,s.jsxs)(o.Zp,{className:"glass dark:glass-dark border-0",children:[(0,s.jsx)(o.aR,{children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(o.ZB,{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Bachelor of Technology - Computer Science"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-gray-600 dark:text-gray-400",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"font-medium",children:"College of Engineering Roorkee"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Roorkee, India"})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col items-end space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"2021 - 2025"})]}),(0,s.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-green-500 to-blue-600 text-white",children:"Final Year"})]})]})}),(0,s.jsx)(o.Wu,{children:(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:"Pursuing Bachelor of Technology in Computer Science with a focus on software development, data structures, algorithms, and modern web technologies. Active participant in coding competitions, hackathons, and technical events."})})]})]})]})})}},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i});var s=t(2596),r=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}},9530:(e,a,t)=>{"use strict";t.d(a,{Leadership:()=>x});var s=t(5155),r=t(2984),i=t(7580),l=t(8186),n=t(6785),o=t(9074),c=t(6695);let d=[{title:"Tech Coordinator",organization:"Disha Committee",duration:"2023 - Present",description:"Leading a team of 70+ volunteers in organizing college-wide technical events and initiatives.",achievements:["Managed 70+ volunteers across multiple technical domains","Organized Manthan Fest - college's flagship technical festival","Coordinated 15+ technical workshops and seminars","Increased student participation by 200% in tech events","Established partnerships with 10+ tech companies"],icon:i.A,color:"from-blue-500 to-purple-600"}],m=[{title:"National Hackathon - 3rd Rank",description:"Secured 3rd position in a prestigious national-level hackathon competing against 500+ teams",icon:l.A,color:"from-yellow-500 to-orange-600",year:"2024"},{title:"Hackathon Organizer",description:"Successfully organized and managed 4 national-level hackathons with 1000+ participants",icon:n.A,color:"from-green-500 to-teal-600",year:"2023-2024"},{title:"Technical Event Management",description:"Led the organization of Manthan Fest, attracting 2000+ students from across the region",icon:o.A,color:"from-purple-500 to-pink-600",year:"2023"}];function x(){return(0,s.jsx)("section",{id:"leadership",className:"py-20 relative",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-16",children:[(0,s.jsx)("h2",{className:"text-4xl md:text-5xl font-bold gradient-text mb-6",children:"Leadership & Achievements"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto",children:"Driving innovation and fostering community growth through leadership"})]}),(0,s.jsxs)("div",{className:"mb-16",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white",children:"Leadership Experience"}),d.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2*a},viewport:{once:!0},children:(0,s.jsxs)(c.Zp,{className:"glass dark:glass-dark border-0 hover:scale-[1.02] transition-all duration-300",children:[(0,s.jsx)(c.aR,{children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-lg bg-gradient-to-r ".concat(e.color),children:(0,s.jsx)(e.icon,{className:"h-6 w-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.ZB,{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.title}),(0,s.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400",children:e.organization})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 dark:text-gray-400",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.duration})]})]})}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[(0,s.jsx)("p",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e.description}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-4",children:"Key Achievements:"}),(0,s.jsx)("ul",{className:"space-y-3",children:e.achievements.map((e,a)=>(0,s.jsxs)(r.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.1*a},viewport:{once:!0},className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-gray-700 dark:text-gray-300 leading-relaxed",children:e})]},a))})]})]})]})},a))]}),(0,s.jsxs)("div",{className:"mb-16",children:[(0,s.jsx)("h3",{className:"text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white",children:"Notable Achievements"}),(0,s.jsx)("div",{className:"grid md:grid-cols-3 gap-8",children:m.map((e,a)=>(0,s.jsx)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.1*a},viewport:{once:!0},children:(0,s.jsx)(c.Zp,{className:"glass dark:glass-dark border-0 h-full hover:scale-105 transition-all duration-300 group",children:(0,s.jsxs)(c.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"inline-flex p-4 rounded-full bg-gradient-to-r ".concat(e.color," mb-4 group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)(e.icon,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("h4",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4 leading-relaxed",children:e.description}),(0,s.jsx)("span",{className:"inline-block px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium",children:e.year})]})})},a))})]}),(0,s.jsxs)(r.P.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:[(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2",children:"70+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Team Members Led"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-green-600 dark:text-green-400 mb-2",children:"4"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Hackathons Organized"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2",children:"2000+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Students Impacted"})]}),(0,s.jsxs)("div",{className:"text-center p-6 glass dark:glass-dark rounded-xl",children:[(0,s.jsx)("div",{className:"text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2",children:"15+"}),(0,s.jsx)("div",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Events Organized"})]})]})]})})}}},e=>{e.O(0,[327,441,964,358],()=>e(e.s=2191)),_N_E=e.O()}]);