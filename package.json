{"name": "abhis<PERSON>k-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18", "react-dom": "^18", "next": "14.0.4", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.0.4", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next-themes": "^0.2.1", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.0.2"}}