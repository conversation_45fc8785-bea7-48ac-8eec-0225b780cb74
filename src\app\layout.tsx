import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON><PERSON><PERSON> – Fullstack Developer & Frontend Enthusiast',
  description: 'Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies.',
  keywords: [
    '<PERSON>bhis<PERSON><PERSON>',
    'Frontend Developer',
    'Full Stack Developer',
    'React Developer',
    'Next.js',
    'IIT Roorkee',
    'College of Engineering Roorkee',
    'Computer Science',
    'Web Developer',
    'JavaScript',
    'TypeScript',
    'Tailwind CSS',
    'Portfolio'
  ],
  authors: [{ name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://abhishek-portfolio.vercel.app',
    title: '<PERSON><PERSON><PERSON><PERSON><PERSON> – Fullstack Developer & Frontend Enthusiast',
    description: 'Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies.',
    siteName: 'Abhishek <PERSON> Portfolio',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Abhishek Kumar Singh - Portfolio',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Abhishek Kumar Singh – Fullstack Developer & Frontend Enthusiast',
    description: 'Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences.',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
  other: {
    'resume-url': '/resume.pdf',
    'resume-download': '/api/resume',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}