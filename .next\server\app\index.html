<!DOCTYPE html><!--l4Q0NMTmhHtPBXa2ZyD85--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=400"/><link rel="preload" as="image" href="https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600"/><link rel="preload" as="image" href="https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600"/><link rel="preload" as="image" href="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600"/><link rel="preload" as="image" href="https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600"/><link rel="stylesheet" href="/_next/static/css/641cf9563d92ca3b.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-bfa3c263a159a400.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-eda38e26c0391a47.js" async=""></script><script src="/_next/static/chunks/main-app-7ab8d74cd128672f.js" async=""></script><script src="/_next/static/chunks/app/layout-f41564b1781e53a9.js" async=""></script><script src="/_next/static/chunks/327-98c3c5aa4ea6ce55.js" async=""></script><script src="/_next/static/chunks/app/page-fe2c1977feea57b0.js" async=""></script><title>Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast</title><meta name="description" content="Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies."/><meta name="author" content="Abhishek Kumar Singh"/><meta name="keywords" content="Abhishek Kumar Singh,Frontend Developer,Full Stack Developer,React Developer,Next.js,IIT Roorkee,College of Engineering Roorkee,Computer Science,Web Developer,JavaScript,TypeScript,Tailwind CSS,Portfolio"/><meta name="creator" content="Abhishek Kumar Singh"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="resume-url" content="/resume.pdf"/><meta name="resume-download" content="/api/resume"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast"/><meta property="og:description" content="Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies."/><meta property="og:url" content="https://abhishek-portfolio.vercel.app"/><meta property="og:site_name" content="Abhishek Kumar Singh Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:image" content="http://localhost:3000/og-image.jpg"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="Abhishek Kumar Singh - Portfolio"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="Abhishek Kumar Singh – Fullstack Developer &amp; Frontend Enthusiast"/><meta name="twitter:description" content="Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences."/><meta name="twitter:image" content="http://localhost:3000/og-image.jpg"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div hidden=""><!--$--><!--/$--></div><main class="relative min-h-screen"><div class="fixed inset-0 overflow-hidden pointer-events-none z-0"></div><nav class="fixed top-0 left-0 right-0 z-50 transition-all duration-500 bg-transparent" style="transform:translateY(-100px)"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex items-center justify-between h-16"><div class="flex-shrink-0" tabindex="0"><a href="#home" class="text-2xl font-bold gradient-text-premium text-shadow-premium hover:text-glow transition-all duration-300">AKS</a></div><div class="hidden md:block"><div class="ml-10 flex items-baseline space-x-4"><a href="#home" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Home</a><a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">About</a><a href="#experience" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Experience</a><a href="#projects" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Projects</a><a href="#skills" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Skills</a><a href="#leadership" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Leadership</a><a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm magnetic-hover" tabindex="0">Contact</a></div></div><div class="flex items-center space-x-4"><a href="/resume.pdf" target="_blank" rel="noopener noreferrer" class="hidden md:flex items-center space-x-2 px-4 py-2 btn-glass-premium text-sm font-medium hover-lift-premium magnetic-hover" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download h-4 w-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg><span>Resume</span></a><div class="md:hidden"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9 glass dark:glass-dark"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-menu h-6 w-6"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg></button></div></div></div></div><div class="md:hidden hidden" style="opacity:0;height:0px"><div class="px-2 pt-2 pb-3 space-y-1 glass-card dark:glass-card-dark backdrop-blur-xl"><a href="#home" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Home</a><a href="#about" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">About</a><a href="#experience" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Experience</a><a href="#projects" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Projects</a><a href="#skills" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Skills</a><a href="#leadership" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Leadership</a><a href="#contact" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200">Contact</a><a href="/resume.pdf" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download h-4 w-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg><span>Download Resume</span></a></div></div></nav><section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden"><div class="absolute inset-0 gradient-bg dark:gradient-bg-dark"></div><div class="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center"><div style="opacity:0;transform:translateY(30px)"><h1 class="text-5xl md:text-7xl font-bold mb-6 text-premium" style="opacity:0;transform:scale(0.5)"><span class="gradient-text-premium text-shadow-premium">Abhishek Kumar Singh</span></h1><div class="text-2xl md:text-3xl font-semibold mb-8 h-12 flex items-center justify-center" style="opacity:0"><span class="text-gray-700 dark:text-gray-300"><span class="typing-cursor text-blue-600"></span></span></div><p class="text-lg md:text-xl text-gray-600 dark:text-gray-400 mb-12 max-w-3xl mx-auto leading-relaxed" style="opacity:0;transform:translateY(20px)">Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about creating exceptional digital experiences with modern web technologies. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and full-stack development.</p><div class="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12" style="opacity:0;transform:translateY(20px)"><div tabindex="0"><button class="inline-flex items-center justify-center whitespace-nowrap focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold transition-all duration-200 h-10 rounded-md px-8 btn-neo-premium text-lg hover-lift-premium magnetic-hover">View My Work</button></div><div tabindex="0"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-10 rounded-md px-8 btn-glass-premium text-lg hover-lift-premium magnetic-hover"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download mr-2 h-5 w-5"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg>Download Resume</button></div></div><div class="flex items-center justify-center space-x-6" style="opacity:0;transform:translateY(20px)"><a href="https://github.com/Abhishek-kumarsingh" target="_blank" rel="noopener noreferrer" class="p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-6 w-6"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/" target="_blank" rel="noopener noreferrer" class="p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin h-6 w-6"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></a><a href="mailto:<EMAIL>" class="p-4 glass-ultra rounded-full hover-lift-premium magnetic-hover glow-advanced shimmer-premium" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-6 w-6"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></a></div></div><div class="absolute bottom-8 left-1/2 transform -translate-x-1/2" style="opacity:0;transform:translateY(20px)"><div class="cursor-pointer"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-down h-6 w-6 text-gray-600 dark:text-gray-400"><path d="M12 5v14"></path><path d="m19 12-7 7-7-7"></path></svg></div></div></div></section><section id="about" class="py-20 relative"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">About Me</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Passionate about crafting digital experiences that make a difference</p></div><div class="grid lg:grid-cols-2 gap-12 items-center"><div class="space-y-6" style="opacity:0;transform:translateX(-30px)"><div class="relative"><div class="w-80 h-80 mx-auto rounded-2xl overflow-hidden neo-brutal bg-gradient-to-br from-blue-400 to-purple-600 p-1"><img src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=400" alt="Abhishek Kumar Singh" class="w-full h-full object-cover rounded-xl"/></div></div><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0"><div class="p-6 space-y-4"><div class="flex items-center space-x-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap h-5 w-5 text-blue-600"><path d="M22 10v6M2 10l10-5 10 5-10 5z"></path><path d="M6 12v5c3 3 9 3 12 0v-5"></path></svg><span class="text-gray-700 dark:text-gray-300">B.Tech Computer Science, College of Engineering Roorkee</span></div><div class="flex items-center space-x-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-5 w-5 text-green-600"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-gray-700 dark:text-gray-300">Graduating 2025</span></div><div class="flex items-center space-x-3"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-5 w-5 text-red-600"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg><span class="text-gray-700 dark:text-gray-300">Roorkee, India</span></div></div></div></div><div class="space-y-6" style="opacity:0;transform:translateX(30px)"><div class="prose prose-lg dark:prose-invert max-w-none"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">I&#x27;m a passionate final-year Computer Science student with a strong focus on<span class="font-semibold text-blue-600 dark:text-blue-400"> frontend development</span> and full-stack capabilities. My journey in tech has been driven by curiosity and a desire to create meaningful digital experiences.</p><p class="text-gray-700 dark:text-gray-300 leading-relaxed">During my internship at <span class="font-semibold text-purple-600 dark:text-purple-400">IIT Roorkee</span>, I had the opportunity to work on high-impact projects, revamping government portals and integrating complex systems. This experience taught me the importance of scalable, maintainable code and user-centered design.</p><p class="text-gray-700 dark:text-gray-300 leading-relaxed">I&#x27;m particularly passionate about <span class="font-semibold text-green-600 dark:text-green-400">React.js</span>,<span class="font-semibold text-blue-600 dark:text-blue-400"> Next.js</span>, and modern web technologies. I enjoy solving complex problems, learning new technologies, and contributing to projects that make a positive impact on users&#x27; lives.</p><p class="text-gray-700 dark:text-gray-300 leading-relaxed">When I&#x27;m not coding, you&#x27;ll find me organizing tech events, mentoring fellow students, or exploring the latest trends in web development and AI. I believe in continuous learning and sharing knowledge with the community.</p></div><div class="grid grid-cols-3 gap-4 mt-8"><div class="text-center p-4 glass dark:glass-dark rounded-xl"><div class="text-2xl font-bold text-blue-600 dark:text-blue-400">30+</div><div class="text-sm text-gray-600 dark:text-gray-400">APIs Integrated</div></div><div class="text-center p-4 glass dark:glass-dark rounded-xl"><div class="text-2xl font-bold text-green-600 dark:text-green-400">45%</div><div class="text-sm text-gray-600 dark:text-gray-400">Bug Reduction</div></div><div class="text-center p-4 glass dark:glass-dark rounded-xl"><div class="text-2xl font-bold text-purple-600 dark:text-purple-400">70+</div><div class="text-sm text-gray-600 dark:text-gray-400">Team Members Led</div></div></div></div></div></div></section><section id="experience" class="py-20 relative"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Experience</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Professional journey and key contributions</p></div><div class="space-y-8"><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-[1.02] transition-all duration-300"><div class="flex flex-col space-y-1.5 p-6"><div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"><div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">Frontend Developer Intern</h3><div class="flex items-center space-x-4 text-gray-600 dark:text-gray-400"><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building h-4 w-4"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg><span class="font-medium">IIT Roorkee</span></div><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-4 w-4"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg><span>Roorkee, India</span></div></div></div><div class="flex flex-col items-end space-y-2"><div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span>Jun 2024 - Aug 2024</span></div><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white">Internship</span></div></div></div><div class="p-6 pt-0 space-y-6"><ul class="space-y-3"><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Revamped a high-traffic government portal using React.js, Tailwind CSS, and Redux Toolkit</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Integrated 30+ RESTful APIs to enhance functionality and user experience</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Reduced system bugs by 45% through comprehensive testing and code optimization</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Collaborated with cross-functional teams to deliver scalable solutions</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Implemented responsive design principles for optimal mobile and desktop experience</span></li></ul><div class="flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">React.js</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Tailwind CSS</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Redux Toolkit</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">REST APIs</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">JavaScript</span></div></div></div></div></div><div class="mt-16" style="opacity:0;transform:translateY(30px)"><h3 class="text-3xl font-bold text-center mb-8 gradient-text">Education</h3><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0"><div class="flex flex-col space-y-1.5 p-6"><div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"><div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">Bachelor of Technology - Computer Science</h3><div class="flex items-center space-x-4 text-gray-600 dark:text-gray-400"><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-building h-4 w-4"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg><span class="font-medium">College of Engineering Roorkee</span></div><div class="flex items-center space-x-2"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-4 w-4"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg><span>Roorkee, India</span></div></div></div><div class="flex flex-col items-end space-y-2"><div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span>2021 - 2025</span></div><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-green-500 to-blue-600 text-white">Final Year</span></div></div></div><div class="p-6 pt-0"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Pursuing Bachelor of Technology in Computer Science with a focus on software development, data structures, algorithms, and modern web technologies. Active participant in coding competitions, hackathons, and technical events.</p></div></div></div></div></section><section id="projects" class="py-20 relative"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Featured Projects</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Showcasing innovative solutions and technical expertise</p></div><div class="grid lg:grid-cols-2 gap-8"><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full group hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="relative overflow-hidden rounded-lg mb-4"><img src="https://images.pexels.com/photos/1181298/pexels-photo-1181298.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600" alt="Threat Zone Prediction System" class="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"/><div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div><div class="absolute top-4 right-4"><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-red-500 to-orange-600 text-white">Completed</span></div></div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">Threat Zone Prediction System</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed">A comprehensive real-time disaster alert platform that leverages machine learning models to predict and monitor potential threat zones with 88% accuracy.</p></div><div class="p-6 pt-0 space-y-6"><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4><ul class="space-y-2"><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Real-time threat monitoring and alerts</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">88% accuracy in disaster prediction</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Interactive dashboard with data visualization</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Multi-source data integration</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Mobile-responsive design</span></li></ul></div><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">React</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Node.js</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">MongoDB</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Machine Learning</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Python</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Express.js</span></div><div class="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link mr-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg>Live Demo</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github mr-2 h-4 w-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>Source Code</button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full group hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="relative overflow-hidden rounded-lg mb-4"><img src="https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600" alt="AI Interview Platform" class="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"/><div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div><div class="absolute top-4 right-4"><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white">Completed</span></div></div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">AI Interview Platform</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed">An intelligent interview platform powered by AI that provides real-time feedback, code evaluation, and comprehensive analytics for both candidates and recruiters.</p></div><div class="p-6 pt-0 space-y-6"><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4><ul class="space-y-2"><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">AI-powered interview questions</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Real-time code evaluation</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Comprehensive analytics dashboard</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Secure authentication system</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Multi-language code support</span></li></ul></div><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Next.js</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Gemini API</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Monaco Editor</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">JWT</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">OAuth</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">TypeScript</span></div><div class="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link mr-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg>Live Demo</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github mr-2 h-4 w-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>Source Code</button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full group hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="relative overflow-hidden rounded-lg mb-4"><img src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600" alt="Learning Management System" class="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"/><div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div><div class="absolute top-4 right-4"><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-green-500 to-teal-600 text-white">In Progress</span></div></div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">Learning Management System</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed">A comprehensive LMS platform with payment integration, real-time communication, and advanced course management features for educators and students.</p></div><div class="p-6 pt-0 space-y-6"><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4><ul class="space-y-2"><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Course creation and management</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Integrated payment system</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Real-time messaging and notifications</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Progress tracking and analytics</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Multi-role user management</span></li></ul></div><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Next.js</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Spring Boot</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Stripe</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">RabbitMQ</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">PostgreSQL</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Redis</span></div><div class="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link mr-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg>Live Demo</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github mr-2 h-4 w-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>Source Code</button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full group hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6 pb-4"><div class="relative overflow-hidden rounded-lg mb-4"><img src="https://images.pexels.com/photos/1181677/pexels-photo-1181677.jpeg?auto=compress&amp;cs=tinysrgb&amp;w=600" alt="Threat Monitoring System" class="w-full h-48 object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"/><div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div><div class="absolute top-4 right-4"><span class="px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-purple-500 to-pink-600 text-white">In Progress</span></div></div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white mb-2">Threat Monitoring System</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed">Advanced threat monitoring system with real-time data processing, machine learning predictions, and comprehensive visualization dashboards.</p></div><div class="p-6 pt-0 space-y-6"><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3">Key Features:</h4><ul class="space-y-2"><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Real-time threat detection</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Machine learning predictions</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Interactive data visualization</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Microservices architecture</span></li><li class="flex items-start space-x-2"><div class="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-sm text-gray-700 dark:text-gray-300">Scalable containerized deployment</span></li></ul></div><div class="flex flex-wrap gap-2"><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">FastAPI</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Spring Boot</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Docker</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">Recharts</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">WebSockets</span><span class="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded text-xs font-medium">XGBoost</span></div><div class="flex space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link mr-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg>Live Demo</button><button class="inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github mr-2 h-4 w-4"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg>Source Code</button></div></div></div></div></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8" style="opacity:0;transform:translateY(30px)"><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-8 w-8 text-blue-600 mx-auto mb-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">4+</div><div class="text-gray-600 dark:text-gray-400">Projects Completed</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-8 w-8 text-green-600 mx-auto mb-4"><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline><polyline points="16 7 22 7 22 13"></polyline></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">88%</div><div class="text-gray-600 dark:text-gray-400">ML Model Accuracy</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-8 w-8 text-purple-600 mx-auto mb-4"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">1000+</div><div class="text-gray-600 dark:text-gray-400">Users Impacted</div></div></div></div></section><section id="skills" class="py-20 relative"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Technical Skills</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Comprehensive expertise across the full development stack</p></div><div class="mb-16" style="opacity:0;transform:translateY(30px)"><h3 class="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">Technology Stack</h3><div class="flex flex-wrap justify-center gap-4"><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">⚛️</span><span class="font-medium text-gray-700 dark:text-gray-300">React</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">▲</span><span class="font-medium text-gray-700 dark:text-gray-300">Next.js</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">📘</span><span class="font-medium text-gray-700 dark:text-gray-300">TypeScript</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🟨</span><span class="font-medium text-gray-700 dark:text-gray-300">JavaScript</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🎨</span><span class="font-medium text-gray-700 dark:text-gray-300">Tailwind</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🟢</span><span class="font-medium text-gray-700 dark:text-gray-300">Node.js</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🍃</span><span class="font-medium text-gray-700 dark:text-gray-300">Spring Boot</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🍃</span><span class="font-medium text-gray-700 dark:text-gray-300">MongoDB</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🐬</span><span class="font-medium text-gray-700 dark:text-gray-300">MySQL</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">🐳</span><span class="font-medium text-gray-700 dark:text-gray-300">Docker</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">📚</span><span class="font-medium text-gray-700 dark:text-gray-300">Git</span></div><div class="flex items-center space-x-2 px-4 py-2 glass-ultra rounded-full cursor-pointer hover-lift-premium magnetic-hover shimmer-premium" style="opacity:0;transform:scale(0.8)"><span class="text-2xl">☁️</span><span class="font-medium text-gray-700 dark:text-gray-300">AWS</span></div></div></div><div class="grid lg:grid-cols-2 gap-8"><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center space-x-3"><div class="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-globe h-6 w-6 text-white"><circle cx="12" cy="12" r="10"></circle><path d="M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20"></path><path d="M2 12h20"></path></svg></div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white">Frontend Development</h3></div></div><div class="p-6 pt-0 space-y-4"><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">React</span><span class="text-sm text-gray-500 dark:text-gray-400">95<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Next.js</span><span class="text-sm text-gray-500 dark:text-gray-400">90<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">TypeScript</span><span class="text-sm text-gray-500 dark:text-gray-400">85<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">JavaScript</span><span class="text-sm text-gray-500 dark:text-gray-400">95<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Tailwind CSS</span><span class="text-sm text-gray-500 dark:text-gray-400">90<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">HTML/CSS</span><span class="text-sm text-gray-500 dark:text-gray-400">95<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500" style="width:0px"></div></div></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center space-x-3"><div class="p-3 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-server h-6 w-6 text-white"><rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect><rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect><line x1="6" x2="6.01" y1="6" y2="6"></line><line x1="6" x2="6.01" y1="18" y2="18"></line></svg></div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white">Backend Development</h3></div></div><div class="p-6 pt-0 space-y-4"><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Node.js</span><span class="text-sm text-gray-500 dark:text-gray-400">85<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Spring Boot</span><span class="text-sm text-gray-500 dark:text-gray-400">80<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">FastAPI</span><span class="text-sm text-gray-500 dark:text-gray-400">75<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Express.js</span><span class="text-sm text-gray-500 dark:text-gray-400">85<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">REST APIs</span><span class="text-sm text-gray-500 dark:text-gray-400">90<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">GraphQL</span><span class="text-sm text-gray-500 dark:text-gray-400">70<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500" style="width:0px"></div></div></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center space-x-3"><div class="p-3 rounded-lg bg-gradient-to-r from-purple-500 to-pink-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database h-6 w-6 text-white"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white">Database &amp; Storage</h3></div></div><div class="p-6 pt-0 space-y-4"><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">MongoDB</span><span class="text-sm text-gray-500 dark:text-gray-400">85<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">MySQL</span><span class="text-sm text-gray-500 dark:text-gray-400">80<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">PostgreSQL</span><span class="text-sm text-gray-500 dark:text-gray-400">75<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Redis</span><span class="text-sm text-gray-500 dark:text-gray-400">70<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Firebase</span><span class="text-sm text-gray-500 dark:text-gray-400">80<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Supabase</span><span class="text-sm text-gray-500 dark:text-gray-400">75<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-purple-500 to-pink-500" style="width:0px"></div></div></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow card-premium border-0 h-full hover-lift-premium hover-tilt"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-center space-x-3"><div class="p-3 rounded-lg bg-gradient-to-r from-orange-500 to-red-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-git-branch h-6 w-6 text-white"><line x1="6" x2="6" y1="3" y2="15"></line><circle cx="18" cy="6" r="3"></circle><circle cx="6" cy="18" r="3"></circle><path d="M18 9a9 9 0 0 1-9 9"></path></svg></div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white">DevOps &amp; Tools</h3></div></div><div class="p-6 pt-0 space-y-4"><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Docker</span><span class="text-sm text-gray-500 dark:text-gray-400">80<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Git/GitHub</span><span class="text-sm text-gray-500 dark:text-gray-400">95<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">Vercel</span><span class="text-sm text-gray-500 dark:text-gray-400">90<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">AWS</span><span class="text-sm text-gray-500 dark:text-gray-400">70<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">RabbitMQ</span><span class="text-sm text-gray-500 dark:text-gray-400">75<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div><div class="space-y-2" style="opacity:0;transform:translateX(-20px)"><div class="flex justify-between items-center"><span class="font-medium text-gray-700 dark:text-gray-300">JWT</span><span class="text-sm text-gray-500 dark:text-gray-400">85<!-- -->%</span></div><div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"><div class="h-2 rounded-full bg-gradient-to-r from-orange-500 to-red-500" style="width:0px"></div></div></div></div></div></div></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8" style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass-card dark:glass-card-dark border-0 text-center hover-lift"><div class="p-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code h-12 w-12 text-blue-600 mx-auto mb-4"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg><h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Clean Code</h3><p class="text-gray-600 dark:text-gray-400">Writing maintainable, scalable, and well-documented code following best practices</p></div></div><div class="rounded-xl bg-card text-card-foreground shadow glass-card dark:glass-card-dark border-0 text-center hover-lift"><div class="p-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-zap h-12 w-12 text-yellow-600 mx-auto mb-4"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg><h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Performance</h3><p class="text-gray-600 dark:text-gray-400">Optimizing applications for speed, efficiency, and excellent user experience</p></div></div><div class="rounded-xl bg-card text-card-foreground shadow glass-card dark:glass-card-dark border-0 text-center hover-lift"><div class="p-6"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-palette h-12 w-12 text-purple-600 mx-auto mb-4"><circle cx="13.5" cy="6.5" r=".5"></circle><circle cx="17.5" cy="10.5" r=".5"></circle><circle cx="8.5" cy="7.5" r=".5"></circle><circle cx="6.5" cy="12.5" r=".5"></circle><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"></path></svg><h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">UI/UX Design</h3><p class="text-gray-600 dark:text-gray-400">Creating intuitive and visually appealing user interfaces with modern design principles</p></div></div></div></div></section><section id="leadership" class="py-20 relative"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Leadership &amp; Achievements</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Driving innovation and fostering community growth through leadership</p></div><div class="mb-16"><h3 class="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">Leadership Experience</h3><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-[1.02] transition-all duration-300"><div class="flex flex-col space-y-1.5 p-6"><div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-6 w-6 text-white"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg></div><div><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white">Tech Coordinator</h3><p class="text-lg text-gray-600 dark:text-gray-400">Disha Committee</p></div></div><div class="flex items-center space-x-2 text-gray-600 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span>2023 - Present</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Leading a team of 70+ volunteers in organizing college-wide technical events and initiatives.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-4">Key Achievements:</h4><ul class="space-y-3"><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Managed 70+ volunteers across multiple technical domains</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Organized Manthan Fest - college&#x27;s flagship technical festival</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Coordinated 15+ technical workshops and seminars</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Increased student participation by 200% in tech events</span></li><li class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div><span class="text-gray-700 dark:text-gray-300 leading-relaxed">Established partnerships with 10+ tech companies</span></li></ul></div></div></div></div></div><div class="mb-16"><h3 class="text-3xl font-bold text-center mb-8 text-gray-900 dark:text-white">Notable Achievements</h3><div class="grid md:grid-cols-3 gap-8"><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-105 transition-all duration-300 group"><div class="p-6 text-center"><div class="inline-flex p-4 rounded-full bg-gradient-to-r from-yellow-500 to-orange-600 mb-4 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trophy h-8 w-8 text-white"><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"></path><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"></path><path d="M4 22h16"></path><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"></path><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"></path><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"></path></svg></div><h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">National Hackathon - 3rd Rank</h4><p class="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">Secured 3rd position in a prestigious national-level hackathon competing against 500+ teams</p><span class="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">2024</span></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-105 transition-all duration-300 group"><div class="p-6 text-center"><div class="inline-flex p-4 rounded-full bg-gradient-to-r from-green-500 to-teal-600 mb-4 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-target h-8 w-8 text-white"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg></div><h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Hackathon Organizer</h4><p class="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">Successfully organized and managed 4 national-level hackathons with 1000+ participants</p><span class="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">2023-2024</span></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-105 transition-all duration-300 group"><div class="p-6 text-center"><div class="inline-flex p-4 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 mb-4 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-8 w-8 text-white"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg></div><h4 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Technical Event Management</h4><p class="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">Led the organization of Manthan Fest, attracting 2000+ students from across the region</p><span class="inline-block px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">2023</span></div></div></div></div></div><div class="grid grid-cols-2 md:grid-cols-4 gap-6" style="opacity:0;transform:translateY(30px)"><div class="text-center p-6 glass dark:glass-dark rounded-xl"><div class="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">70+</div><div class="text-gray-600 dark:text-gray-400 text-sm">Team Members Led</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><div class="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">4</div><div class="text-gray-600 dark:text-gray-400 text-sm">Hackathons Organized</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><div class="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">2000+</div><div class="text-gray-600 dark:text-gray-400 text-sm">Students Impacted</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><div class="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">15+</div><div class="text-gray-600 dark:text-gray-400 text-sm">Events Organized</div></div></div></div></section><section id="certifications" class="py-20 relative"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Certifications</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Continuous learning and professional development achievements</p></div><div class="grid lg:grid-cols-2 gap-8"><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-start justify-between"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 text-2xl">⚛️</div><div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white mb-1">React Development</h3><p class="text-gray-600 dark:text-gray-400 font-medium">Board Infinity</p></div></div><div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-sm">2024</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Comprehensive certification covering React fundamentals, hooks, state management, and modern React patterns.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-4 w-4 mr-2 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg>Skills Covered:</h4><div class="flex flex-wrap gap-2"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">React.js</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">JSX</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Hooks</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">State Management</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Component Architecture</span></div></div><div class="pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs w-full group-hover:scale-105 transition-transform duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-2 h-4 w-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg>View Credential<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-start justify-between"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-orange-500 to-red-500 text-2xl">🌐</div><div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white mb-1">HTML, CSS &amp; JavaScript</h3><p class="text-gray-600 dark:text-gray-400 font-medium">Coursera</p></div></div><div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-sm">2023</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Complete web development fundamentals covering HTML5, CSS3, and modern JavaScript ES6+ features.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-4 w-4 mr-2 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg>Skills Covered:</h4><div class="flex flex-wrap gap-2"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">HTML5</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">CSS3</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">JavaScript</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">DOM Manipulation</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Responsive Design</span></div></div><div class="pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs w-full group-hover:scale-105 transition-transform duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-2 h-4 w-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg>View Credential<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-start justify-between"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-2xl">🔒</div><div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white mb-1">Cybersecurity Fundamentals</h3><p class="text-gray-600 dark:text-gray-400 font-medium">IBM</p></div></div><div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-sm">2023</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Comprehensive cybersecurity training covering threat analysis, security protocols, and best practices.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-4 w-4 mr-2 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg>Skills Covered:</h4><div class="flex flex-wrap gap-2"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Security Protocols</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Threat Analysis</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Risk Assessment</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Network Security</span></div></div><div class="pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs w-full group-hover:scale-105 transition-transform duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-2 h-4 w-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg>View Credential<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-start justify-between"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-green-500 to-teal-500 text-2xl">☕</div><div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white mb-1">Java Programming</h3><p class="text-gray-600 dark:text-gray-400 font-medium">GeeksforGeeks</p></div></div><div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-sm">2023</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Advanced Java programming certification covering OOP concepts, data structures, and algorithms.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-4 w-4 mr-2 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg>Skills Covered:</h4><div class="flex flex-wrap gap-2"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Java</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">OOP</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Data Structures</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Algorithms</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Problem Solving</span></div></div><div class="pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs w-full group-hover:scale-105 transition-transform duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-2 h-4 w-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg>View Credential<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></button></div></div></div></div><div style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 h-full hover:scale-[1.02] transition-all duration-300 group"><div class="flex flex-col space-y-1.5 p-6"><div class="flex items-start justify-between"><div class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-purple-500 to-indigo-500 text-2xl">🚀</div><div><h3 class="tracking-tight text-xl font-bold text-gray-900 dark:text-white mb-1">Full Stack Development Bootcamp</h3><p class="text-gray-600 dark:text-gray-400 font-medium">GeeksforGeeks</p></div></div><div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-4 w-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><span class="text-sm">2024</span></div></div></div><div class="p-6 pt-0 space-y-6"><p class="text-gray-700 dark:text-gray-300 leading-relaxed">Intensive full-stack development program covering both frontend and backend technologies.</p><div><h4 class="font-semibold text-gray-900 dark:text-white mb-3 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-4 w-4 mr-2 text-green-600"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg>Skills Covered:</h4><div class="flex flex-wrap gap-2"><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Full Stack</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">MERN Stack</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Database Design</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">API Development</span><span class="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium hover:scale-105 transition-transform duration-200" style="opacity:0;transform:scale(0.8)">Deployment</span></div></div><div class="pt-4 border-t border-gray-200 dark:border-gray-700"><button class="inline-flex items-center justify-center whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-8 rounded-md px-3 text-xs w-full group-hover:scale-105 transition-transform duration-200"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award mr-2 h-4 w-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg>View Credential<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-external-link ml-2 h-4 w-4"><path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path><polyline points="15 3 21 3 21 9"></polyline><line x1="10" x2="21" y1="14" y2="3"></line></svg></button></div></div></div></div></div><div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8" style="opacity:0;transform:translateY(30px)"><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award h-8 w-8 text-yellow-600 mx-auto mb-4"><circle cx="12" cy="8" r="6"></circle><path d="M15.477 12.89 17 22l-5-3-5 3 1.523-9.11"></path></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">5+</div><div class="text-gray-600 dark:text-gray-400">Professional Certifications</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-check-circle h-8 w-8 text-green-600 mx-auto mb-4"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><path d="m9 11 3 3L22 4"></path></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">100%</div><div class="text-gray-600 dark:text-gray-400">Completion Rate</div></div><div class="text-center p-6 glass dark:glass-dark rounded-xl"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-8 w-8 text-blue-600 mx-auto mb-4"><rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect><line x1="16" x2="16" y1="2" y2="6"></line><line x1="8" x2="8" y1="2" y2="6"></line><line x1="3" x2="21" y1="10" y2="10"></line></svg><div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">2024</div><div class="text-gray-600 dark:text-gray-400">Latest Certification</div></div></div></div></section><section id="contact" class="py-20 relative"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="text-center mb-16" style="opacity:0;transform:translateY(30px)"><h2 class="text-4xl md:text-5xl font-bold gradient-text mb-6">Let&#x27;s Connect</h2><p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">Ready to collaborate on exciting projects or discuss opportunities? I&#x27;d love to hear from you!</p></div><div class="grid lg:grid-cols-2 gap-12 items-start"><div class="space-y-8" style="opacity:0;transform:translateX(-30px)"><div><h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Get in Touch</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed mb-8">I&#x27;m always open to discussing new opportunities, interesting projects, or just having a chat about technology and development. Whether you&#x27;re looking for a frontend developer, need help with a project, or want to collaborate on something exciting, feel free to reach out!</p></div><div class="space-y-4"><div style="opacity:0;transform:translateY(20px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-105 transition-all duration-300 group cursor-pointer"><div class="p-4"><a href="mailto:<EMAIL>" target="_self" rel="" class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-5 w-5 text-white"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></div><div><p class="font-medium text-gray-900 dark:text-white">Email</p><p class="text-gray-600 dark:text-gray-400"><EMAIL></p></div></a></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-105 transition-all duration-300 group cursor-pointer"><div class="p-4"><a href="https://github.com/Abhishek-kumarsingh" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-gray-700 to-gray-900 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-5 w-5 text-white"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></div><div><p class="font-medium text-gray-900 dark:text-white">GitHub</p><p class="text-gray-600 dark:text-gray-400">Abhishek-kumarsingh</p></div></a></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-105 transition-all duration-300 group cursor-pointer"><div class="p-4"><a href="https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-blue-600 to-blue-800 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin h-5 w-5 text-white"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></div><div><p class="font-medium text-gray-900 dark:text-white">LinkedIn</p><p class="text-gray-600 dark:text-gray-400">abhishek-kumar-singh</p></div></a></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 hover:scale-105 transition-all duration-300 group cursor-pointer"><div class="p-4"><a href="#" target="_self" rel="" class="flex items-center space-x-4"><div class="p-3 rounded-lg bg-gradient-to-r from-green-500 to-emerald-600 group-hover:scale-110 transition-transform duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-map-pin h-5 w-5 text-white"><path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path><circle cx="12" cy="10" r="3"></circle></svg></div><div><p class="font-medium text-gray-900 dark:text-white">Location</p><p class="text-gray-600 dark:text-gray-400">Roorkee, India</p></div></a></div></div></div></div><div class="space-y-4" style="opacity:0;transform:translateY(20px)"><h4 class="text-lg font-semibold text-gray-900 dark:text-white">Quick Actions</h4><div class="flex flex-col sm:flex-row gap-4"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold transition-all duration-200 h-9 px-4 py-2 flex-1"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail mr-2 h-4 w-4"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>Send Email</button><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 flex-1 btn-glass-premium hover-lift-premium"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download mr-2 h-4 w-4"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg>Download Resume</button></div></div></div><div style="opacity:0;transform:translateX(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0"><div class="flex flex-col space-y-1.5 p-6"><h3 class="tracking-tight text-2xl font-bold text-gray-900 dark:text-white flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-message-circle mr-3 h-6 w-6"><path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path></svg>Send a Message</h3></div><div class="p-6 pt-0 space-y-6"><form class="space-y-6"><div class="grid grid-cols-1 sm:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name</label><input type="text" class="w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="John"/></div><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name</label><input type="text" class="w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="Doe"/></div></div><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email</label><input type="email" class="w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="<EMAIL>"/></div><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Subject</label><input type="text" class="w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="Project Collaboration"/></div><div><label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Message</label><textarea rows="5" class="w-full px-4 py-3 rounded-lg glass dark:glass-dark border border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none" placeholder="Tell me about your project or opportunity..."></textarea></div><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold transition-all duration-200 h-9 px-4 py-2 w-full" type="submit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-send mr-2 h-4 w-4"><path d="m22 2-7 20-4-9-9-4Z"></path><path d="M22 2 11 13"></path></svg>Send Message</button></form><div class="text-center pt-4 border-t border-gray-200 dark:border-gray-700"><p class="text-sm text-gray-600 dark:text-gray-400">Prefer email? Reach me directly at<!-- --> <a href="mailto:<EMAIL>" class="text-blue-600 dark:text-blue-400 hover:underline font-medium"><EMAIL></a></p></div></div></div></div></div><div class="mt-16 text-center" style="opacity:0;transform:translateY(30px)"><div class="rounded-xl bg-card text-card-foreground shadow glass dark:glass-dark border-0 max-w-2xl mx-auto"><div class="p-8"><h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">Ready to Build Something Amazing?</h3><p class="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">I&#x27;m currently open to new opportunities and exciting projects. Let&#x27;s discuss how we can work together to create something extraordinary!</p><div class="flex flex-col sm:flex-row gap-4 justify-center"><button class="inline-flex items-center justify-center whitespace-nowrap text-sm focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 neo-brutal neo-brutal-hover bg-yellow-400 text-black font-bold transition-all duration-200 h-10 rounded-md px-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail mr-2 h-5 w-5"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg>Start a Conversation</button><button class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-10 rounded-md px-8 glass dark:glass-dark"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin mr-2 h-5 w-5"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg>Connect on LinkedIn</button></div></div></div></div></div></section><footer class="relative py-12 border-t border-gray-200 dark:border-gray-800"><div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-center mb-8" style="opacity:0;transform:translateY(20px)"><button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 w-9 glass dark:glass-dark hover:scale-110 transition-all duration-300"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up h-4 w-4"><path d="m5 12 7-7 7 7"></path><path d="M12 19V5"></path></svg></button></div><div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8"><div class="text-center md:text-left" style="opacity:0;transform:translateY(20px)"><h3 class="text-2xl font-bold gradient-text mb-4">Abhishek Kumar Singh</h3><p class="text-gray-600 dark:text-gray-400 leading-relaxed">Frontend Developer &amp; Full Stack Enthusiast passionate about creating exceptional digital experiences with modern web technologies.</p></div><div class="text-center" style="opacity:0;transform:translateY(20px)"><h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Links</h4><div class="space-y-2"><div><a href="#about" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">About</a></div><div><a href="#projects" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Projects</a></div><div><a href="#skills" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Skills</a></div><div><a href="#experience" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Experience</a></div><div><a href="#contact" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">Contact</a></div></div></div><div class="text-center md:text-right" style="opacity:0;transform:translateY(20px)"><h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Connect With Me</h4><div class="flex justify-center md:justify-end space-x-4 mb-4"><a href="https://github.com/Abhishek-kumarsingh" target="_blank" rel="noopener noreferrer" class="p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-github h-5 w-5"><path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path><path d="M9 18c-4.51 2-5-2-7-2"></path></svg></a><a href="https://www.linkedin.com/in/abhishek-kumar-singh-3932ab289/" target="_blank" rel="noopener noreferrer" class="p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin h-5 w-5"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></a><a href="mailto:<EMAIL>" class="p-2 glass dark:glass-dark rounded-full hover:scale-110 transition-all duration-300" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mail h-5 w-5"><rect width="20" height="16" x="2" y="4" rx="2"></rect><path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"></path></svg></a></div><p class="text-gray-600 dark:text-gray-400 text-sm"><EMAIL></p><p class="text-gray-600 dark:text-gray-400 text-sm">Roorkee, India</p></div></div><div class="pt-8 border-t border-gray-200 dark:border-gray-700" style="opacity:0;transform:translateY(20px)"><div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0"><p class="text-gray-600 dark:text-gray-400 text-sm flex items-center">© 2024 Abhishek Kumar Singh. Made with<!-- --> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-heart h-4 w-4 text-red-500 mx-1"><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"></path></svg>using Next.js &amp; Tailwind CSS</p><div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400"><span>Final Year B.Tech CS Student</span><span>•</span><span>College of Engineering Roorkee</span></div></div></div></div></footer></main><!--$--><!--/$--><script src="/_next/static/chunks/webpack-bfa3c263a159a400.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[1483,[\"177\",\"static/chunks/app/layout-f41564b1781e53a9.js\"],\"ThemeProvider\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[2836,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"FloatingShapes\"]\n6:I[1440,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Navigation\"]\n7:I[7711,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Hero\"]\n8:I[4334,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"About\"]\n9:I[9067,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Experience\"]\na:I[8957,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Projects\"]\nb:I[8397,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Skills\"]\nc:I[9530,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Leadership\"]\nd:I[1634,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Certifications\"]\ne:I[433,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Contact\"]\nf:I[3430,[\"327\",\"static/chunks/327-98c3c5aa4ea6ce55.js\",\"974\",\"static/chunks/app/page-fe2c1977feea57b0.js\"],\"Footer\"]\n10:I[9665,[],\"OutletBoundary\"]\n12:I[4911,[],\"AsyncMetadataOutlet\"]\n14:I[9665,[],\"ViewportBoundary\"]\n16:I[9665,[],\"MetadataBoundary\"]\n17:\"$Sreact.suspense\"\n19:I[8393,[],\"\"]\n:HL[\"/_next/static/css/641cf9563d92ca3b.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"l4Q0NMTmhHtPBXa2ZyD85\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/641cf9563d92ca3b.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"children\":[\"$\",\"$L2\",null,{\"attribute\":\"class\",\"defaultTheme\":\"system\",\"enableSystem\":true,\"disableTransitionOnChange\":true,\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"main\",null,{\"className\":\"relative min-h-screen\",\"children\":[[\"$\",\"$L5\",null,{}],[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{}],[\"$\",\"$L8\",null,{}],[\"$\",\"$L9\",null,{}],[\"$\",\"$La\",null,{}],[\"$\",\"$Lb\",null,{}],[\"$\",\"$Lc\",null,{}],[\"$\",\"$Ld\",null,{}],[\"$\",\"$Le\",null,{}],[\"$\",\"$Lf\",null,{}]]}],null,[\"$\",\"$L10\",null,{\"children\":[\"$L11\",[\"$\",\"$L12\",null,{\"promise\":\"$@13\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$L14\",null,{\"children\":\"$L15\"}],null],[\"$\",\"$L16\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$17\",null,{\"fallback\":null,\"children\":\"$L18\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$19\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n11:null\n"])</script><script>self.__next_f.push([1,"13:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Final-year B.Tech Computer Science student at College of Engineering Roorkee, passionate about frontend development and full-stack capabilities. Former Frontend Developer Intern at IIT Roorkee with expertise in React, Next.js, and modern web technologies.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"Abhishek Kumar Singh\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"Abhishek Kumar Singh,Frontend Developer,Full Stack Developer,React Developer,Next.js,IIT Roorkee,College of Engineering Roorkee,Computer Science,Web Developer,JavaScript,TypeScript,Tailwind CSS,Portfolio\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"Abhishek Kumar Singh\"}],[\"$\",\"meta\",\"5\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"6\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"7\",{\"name\":\"resume-url\",\"content\":\"/resume.pdf\"}],[\"$\",\"meta\",\"8\",{\"name\":\"resume-download\",\"content\":\"/api/resume\"}],[\"$\",\"meta\",\"9\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:title\",\"content\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:description\",\"content\":\"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences with modern web technologies.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:url\",\"content\":\"https://abhishek-portfolio.vercel.app\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:site_name\",\"content\":\"Abhishek Kumar Singh Portfolio\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"18\",{\"property\":\"og:image:alt\",\"content\":\"Abhishek Kumar Singh - Portfolio\"}],[\"$\",\"meta\",\"19\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:title\",\"content\":\"Abhishek Kumar Singh – Fullstack Developer \u0026 Frontend Enthusiast\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:description\",\"content\":\"Final-year B.Tech Computer Science student passionate about creating exceptional digital experiences.\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image\",\"content\":\"http://localhost:3000/og-image.jpg\"}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"18:\"$13:metadata\"\n"])</script></body></html>